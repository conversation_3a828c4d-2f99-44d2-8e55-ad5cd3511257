{"name": "fieldreporter-monorepo", "version": "1.0.0", "description": "FieldReporter - Modern Field Reporting Platform", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"], "keywords": ["fieldreporter", "forms", "api", "react", "nodejs"], "author": "FieldReporter Team", "license": "MIT"}