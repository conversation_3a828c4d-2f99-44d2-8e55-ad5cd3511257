const { v4: uuidv4 } = require('uuid');
const database = require('../config/database');

// Get all submissions accessible to user
const getSubmissions = async (req, res) => {
  try {
    const user = req.user;
    const { 
      page = 1, 
      limit = 10, 
      form_id, 
      status, 
      start_date, 
      end_date,
      user_id 
    } = req.query;
    
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    let params = {};

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        // Can see all submissions in their next_department
        whereClause += ` AND EXISTS (
          SELECT 1 FROM forms f 
          WHERE f.id = fs.form_id 
            AND f.general_department = @generalDepartment 
            AND f.next_department = @nextDepartment
        )`;
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        // Can only see submissions from their exact department
        whereClause += ` AND EXISTS (
          SELECT 1 FROM forms f 
          WHERE f.id = fs.form_id 
            AND f.general_department = @generalDepartment 
            AND f.next_department = @nextDepartment 
            AND f.direct_department = @directDepartment
        )`;
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    // Form filter
    if (form_id) {
      whereClause += ' AND fs.form_id = @formId';
      params.formId = form_id;
    }

    // Status filter
    if (status) {
      whereClause += ' AND fs.status = @status';
      params.status = status;
    }

    // Date range filter
    if (start_date) {
      whereClause += ' AND fs.created_at >= @startDate';
      params.startDate = start_date;
    }

    if (end_date) {
      whereClause += ' AND fs.created_at <= @endDate';
      params.endDate = end_date;
    }

    // User filter (for admins or managers)
    if (user_id && (user.role === 'admin' || user.role === 'final_approval')) {
      whereClause += ' AND fs.user_id = @userId';
      params.userId = user_id;
    }

    // Get submissions with pagination
    const query = `
      SELECT 
        fs.id, fs.form_id, fs.user_id, fs.status,
        fs.latitude, fs.longitude, fs.location_name,
        fs.time_spent, fs.created_at, fs.updated_at,
        fs.approved_at, fs.final_approved_at,
        f.name as form_name, f.form_type,
        u.username as submitted_by,
        au.username as approved_by_name,
        fau.username as final_approved_by_name,
        (SELECT COUNT(*) FROM submission_attachments sa WHERE sa.submission_id = fs.id) as attachment_count
      FROM form_submissions fs
      LEFT JOIN forms f ON fs.form_id = f.id
      LEFT JOIN users u ON fs.user_id = u.id
      LEFT JOIN users au ON fs.approved_by = au.id
      LEFT JOIN users fau ON fs.final_approved_by = fau.id
      ${whereClause}
      ORDER BY fs.created_at DESC
      OFFSET @offset ROWS
      FETCH NEXT @limit ROWS ONLY
    `;

    params.offset = offset;
    params.limit = parseInt(limit);

    const result = await database.query(query, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM form_submissions fs
      LEFT JOIN forms f ON fs.form_id = f.id
      ${whereClause}
    `;
    
    const countResult = await database.query(countQuery, params);
    const total = countResult.recordset[0].total;

    res.json({
      success: true,
      data: {
        submissions: result.recordset,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get submissions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get submissions'
    });
  }
};

// Get single submission by ID
const getSubmissionById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    let whereClause = 'WHERE fs.id = @submissionId';
    let params = { submissionId: id };

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        whereClause += ` AND EXISTS (
          SELECT 1 FROM forms f 
          WHERE f.id = fs.form_id 
            AND f.general_department = @generalDepartment 
            AND f.next_department = @nextDepartment
        )`;
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        whereClause += ` AND (fs.user_id = @userId OR EXISTS (
          SELECT 1 FROM forms f 
          WHERE f.id = fs.form_id 
            AND f.general_department = @generalDepartment 
            AND f.next_department = @nextDepartment 
            AND f.direct_department = @directDepartment
        ))`;
        params.userId = user.id;
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    const query = `
      SELECT 
        fs.id, fs.form_id, fs.user_id, fs.submission_data, fs.status,
        fs.approval_notes, fs.latitude, fs.longitude, fs.location_name,
        fs.time_spent, fs.created_at, fs.updated_at,
        fs.approved_at, fs.final_approved_at,
        f.name as form_name, f.form_type, f.json_schema, f.excel_structure,
        u.username as submitted_by,
        au.username as approved_by_name,
        fau.username as final_approved_by_name
      FROM form_submissions fs
      LEFT JOIN forms f ON fs.form_id = f.id
      LEFT JOIN users u ON fs.user_id = u.id
      LEFT JOIN users au ON fs.approved_by = au.id
      LEFT JOIN users fau ON fs.final_approved_by = fau.id
      ${whereClause}
    `;

    const result = await database.query(query, params);

    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Submission not found or access denied'
      });
    }

    const submission = result.recordset[0];

    // Parse JSON fields
    if (submission.submission_data) {
      try {
        submission.submission_data = JSON.parse(submission.submission_data);
      } catch (error) {
        console.error('Error parsing submission data:', error);
      }
    }

    if (submission.json_schema) {
      try {
        submission.json_schema = JSON.parse(submission.json_schema);
      } catch (error) {
        console.error('Error parsing JSON schema:', error);
      }
    }

    if (submission.excel_structure) {
      try {
        submission.excel_structure = JSON.parse(submission.excel_structure);
      } catch (error) {
        console.error('Error parsing Excel structure:', error);
      }
    }

    // Get attachments
    const attachmentsResult = await database.query(
      'SELECT id, file_name, file_size, file_type, uploaded_at FROM submission_attachments WHERE submission_id = @submissionId',
      { submissionId: id }
    );

    submission.attachments = attachmentsResult.recordset;

    res.json({
      success: true,
      data: { submission }
    });

  } catch (error) {
    console.error('Get submission by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get submission'
    });
  }
};

// Create new submission
const createSubmission = async (req, res) => {
  try {
    const {
      form_id,
      submission_data,
      status = 'submitted',
      latitude,
      longitude,
      location_name,
      time_spent
    } = req.body;

    const user = req.user;
    const submissionId = uuidv4();

    // Verify form exists and user has access
    const formResult = await database.query(`
      SELECT id, name 
      FROM forms 
      WHERE id = @formId 
        AND is_active = 1
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
    `, {
      formId: form_id,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (formResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form not found or access denied'
      });
    }

    // Create submission
    await database.query(`
      INSERT INTO form_submissions (
        id, form_id, user_id, submission_data, status,
        latitude, longitude, location_name, time_spent
      ) VALUES (
        @submissionId, @formId, @userId, @submissionData, @status,
        @latitude, @longitude, @locationName, @timeSpent
      )
    `, {
      submissionId,
      formId: form_id,
      userId: user.id,
      submissionData: JSON.stringify(submission_data),
      status,
      latitude: latitude || null,
      longitude: longitude || null,
      locationName: location_name || null,
      timeSpent: time_spent || null
    });

    // Get created submission
    const submissionResult = await database.query(
      'SELECT id, form_id, status, created_at FROM form_submissions WHERE id = @submissionId',
      { submissionId }
    );

    res.status(201).json({
      success: true,
      message: 'Submission created successfully',
      data: { submission: submissionResult.recordset[0] }
    });

  } catch (error) {
    console.error('Create submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create submission'
    });
  }
};

// Update submission status (approve/reject)
const updateSubmissionStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, approval_notes } = req.body;
    const user = req.user;

    // Validate status
    const validStatuses = ['draft', 'submitted', 'approved', 'rejected', 'final_approved'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    // Get current submission
    const submissionResult = await database.query(`
      SELECT fs.id, fs.status, fs.user_id, f.general_department, f.next_department, f.direct_department
      FROM form_submissions fs
      LEFT JOIN forms f ON fs.form_id = f.id
      WHERE fs.id = @submissionId
    `, { submissionId: id });

    if (submissionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Submission not found'
      });
    }

    const submission = submissionResult.recordset[0];

    // Check permissions
    let canUpdate = false;

    if (user.role === 'admin') {
      canUpdate = true;
    } else if (status === 'final_approved' && user.role === 'final_approval') {
      // Final approval can approve if in same department hierarchy
      canUpdate = submission.general_department === user.general_department &&
                  submission.next_department === user.next_department;
    } else if ((status === 'approved' || status === 'rejected') && user.role === 'approval') {
      // Approval user can approve/reject if in same department
      canUpdate = submission.general_department === user.general_department &&
                  submission.next_department === user.next_department &&
                  submission.direct_department === user.direct_department;
    } else if (status === 'draft' && submission.user_id === user.id) {
      // User can change their own submission to draft
      canUpdate = true;
    }

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update this submission'
      });
    }

    // Update submission
    const updateFields = ['status = @status', 'updated_at = GETDATE()'];
    const params = { submissionId: id, status };

    if (approval_notes !== undefined) {
      updateFields.push('approval_notes = @approvalNotes');
      params.approvalNotes = approval_notes;
    }

    if (status === 'approved') {
      updateFields.push('approved_by = @approvedBy', 'approved_at = GETDATE()');
      params.approvedBy = user.id;
    } else if (status === 'final_approved') {
      updateFields.push('final_approved_by = @finalApprovedBy', 'final_approved_at = GETDATE()');
      params.finalApprovedBy = user.id;
    }

    const updateQuery = `
      UPDATE form_submissions 
      SET ${updateFields.join(', ')}
      WHERE id = @submissionId
    `;

    await database.query(updateQuery, params);

    // Get updated submission
    const updatedSubmission = await database.query(
      'SELECT id, status, approved_at, final_approved_at, updated_at FROM form_submissions WHERE id = @submissionId',
      { submissionId: id }
    );

    res.json({
      success: true,
      message: 'Submission status updated successfully',
      data: { submission: updatedSubmission.recordset[0] }
    });

  } catch (error) {
    console.error('Update submission status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update submission status'
    });
  }
};

// Delete submission
const deleteSubmission = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Get submission
    const submissionResult = await database.query(
      'SELECT id, user_id, status FROM form_submissions WHERE id = @submissionId',
      { submissionId: id }
    );

    if (submissionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Submission not found'
      });
    }

    const submission = submissionResult.recordset[0];

    // Check permissions (only creator or admin can delete, and only if not final_approved)
    if (user.role !== 'admin' && submission.user_id !== user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own submissions'
      });
    }

    if (submission.status === 'final_approved') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete final approved submissions'
      });
    }

    // Delete attachments first
    await database.query(
      'DELETE FROM submission_attachments WHERE submission_id = @submissionId',
      { submissionId: id }
    );

    // Delete submission
    await database.query(
      'DELETE FROM form_submissions WHERE id = @submissionId',
      { submissionId: id }
    );

    res.json({
      success: true,
      message: 'Submission deleted successfully'
    });

  } catch (error) {
    console.error('Delete submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete submission'
    });
  }
};

// Get submission statistics
const getSubmissionStats = async (req, res) => {
  try {
    const user = req.user;
    const { start_date, end_date } = req.query;

    let whereClause = 'WHERE 1=1';
    let params = {};

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        whereClause += ` AND EXISTS (
          SELECT 1 FROM forms f 
          WHERE f.id = fs.form_id 
            AND f.general_department = @generalDepartment 
            AND f.next_department = @nextDepartment
        )`;
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        whereClause += ` AND EXISTS (
          SELECT 1 FROM forms f 
          WHERE f.id = fs.form_id 
            AND f.general_department = @generalDepartment 
            AND f.next_department = @nextDepartment 
            AND f.direct_department = @directDepartment
        )`;
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    // Date range filter
    if (start_date) {
      whereClause += ' AND fs.created_at >= @startDate';
      params.startDate = start_date;
    }

    if (end_date) {
      whereClause += ' AND fs.created_at <= @endDate';
      params.endDate = end_date;
    }

    const statsQuery = `
      SELECT 
        COUNT(*) as total_submissions,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_count,
        SUM(CASE WHEN status = 'submitted' THEN 1 ELSE 0 END) as submitted_count,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        SUM(CASE WHEN status = 'final_approved' THEN 1 ELSE 0 END) as final_approved_count,
        AVG(CAST(time_spent as FLOAT)) as avg_time_spent
      FROM form_submissions fs
      ${whereClause}
    `;

    const statsResult = await database.query(statsQuery, params);

    res.json({
      success: true,
      data: {
        statistics: statsResult.recordset[0]
      }
    });

  } catch (error) {
    console.error('Get submission stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get submission statistics'
    });
  }
};

module.exports = {
  getSubmissions,
  getSubmissionById,
  createSubmission,
  updateSubmissionStatus,
  deleteSubmission,
  getSubmissionStats
};