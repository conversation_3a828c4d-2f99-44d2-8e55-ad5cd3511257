const express = require('express');
const router = express.Router();
const checksheetController = require('../controllers/checksheetController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { validateChecksheetSession, validateChecksheetResponse, validateUUID } = require('../middleware/validation');

// Get all checksheet sessions for user
router.get('/sessions', 
  authenticateToken, 
  checksheetController.getChecksheetSessions
);

// Get single checksheet session
router.get('/sessions/:id', 
  authenticateToken, 
  validateUUID('id'),
  checksheetController.getChecksheetSessionById
);

// Start new checksheet session
router.post('/sessions', 
  authenticateToken, 
  requirePermission('perform_checksheet'),
  validateChecksheetSession,
  checksheetController.startChecksheetSession
);

// Save checksheet response
router.post('/sessions/:id/responses', 
  authenticateToken,
  validateUUID('id'),
  validateChecksheetResponse,
  checksheetController.saveChecksheetResponse
);

// Complete checksheet session
router.patch('/sessions/:id/complete', 
  authenticateToken,
  validateUUID('id'),
  checksheetController.completeChecksheetSession
);

// Get checksheet responses for session
router.get('/sessions/:id/responses', 
  authenticateToken,
  validateUUID('id'),
  checksheetController.getChecksheetResponses
);

// Upload attachment for checksheet
router.post('/sessions/:id/attachments', 
  authenticateToken,
  validateUUID('id'),
  // TODO: Add file upload middleware
  checksheetController.uploadChecksheetAttachment
);

// Get form templates
router.get('/templates', 
  authenticateToken,
  checksheetController.getFormTemplates
);

module.exports = router;