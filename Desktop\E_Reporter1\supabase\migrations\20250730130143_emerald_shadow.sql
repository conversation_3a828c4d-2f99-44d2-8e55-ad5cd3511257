-- FieldReporter Database Schema for SQL Server
-- Created: 2024-01-20
-- Description: Complete database schema for <PERSON><PERSON><PERSON>orter application

USE master;
GO

-- Create database if not exists
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'FieldReporter')
BEGIN
    CREATE DATABASE FieldReporter;
END
GO

USE FieldReporter;
GO

-- =============================================
-- 1. USERS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        username NVARCHAR(100) NOT NULL UNIQUE,
        email NVARCHAR(255) NOT NULL UNIQUE,
        password_hash NVARCHAR(255) NOT NULL,
        role NVARCHAR(50) NOT NULL CHECK (role IN ('admin', 'submit', 'approval', 'final_approval')),
        general_department NVARCHAR(255) NOT NULL,
        next_department NVARCHAR(255) NOT NULL,
        direct_department NVARCHAR(255) NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        last_login DATETIME2 NULL
    );
    
    -- Create indexes
    CREATE INDEX IX_users_email ON users(email);
    CREATE INDEX IX_users_role ON users(role);
    CREATE INDEX IX_users_department ON users(general_department, next_department, direct_department);
    
    PRINT 'Table [users] created successfully';
END
GO

-- =============================================
-- 2. DEPARTMENTS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='departments' AND xtype='U')
BEGIN
    CREATE TABLE departments (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        type NVARCHAR(50) NOT NULL CHECK (type IN ('general', 'next', 'direct')),
        parent_id UNIQUEIDENTIFIER NULL,
        description NVARCHAR(MAX) NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (parent_id) REFERENCES departments(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_departments_type ON departments(type);
    CREATE INDEX IX_departments_parent ON departments(parent_id);
    
    PRINT 'Table [departments] created successfully';
END
GO

-- =============================================
-- 3. FOLDERS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='folders' AND xtype='U')
BEGIN
    CREATE TABLE folders (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX) NULL,
        parent_id UNIQUEIDENTIFIER NULL,
        general_department NVARCHAR(255) NOT NULL,
        next_department NVARCHAR(255) NOT NULL,
        direct_department NVARCHAR(255) NOT NULL,
        created_by UNIQUEIDENTIFIER NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (parent_id) REFERENCES folders(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_folders_parent ON folders(parent_id);
    CREATE INDEX IX_folders_department ON folders(general_department, next_department, direct_department);
    CREATE INDEX IX_folders_created_by ON folders(created_by);
    
    PRINT 'Table [folders] created successfully';
END
GO

-- =============================================
-- 4. FORMS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='forms' AND xtype='U')
BEGIN
    CREATE TABLE forms (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX) NULL,
        folder_id UNIQUEIDENTIFIER NULL,
        form_type NVARCHAR(50) DEFAULT 'survey' CHECK (form_type IN ('survey', 'excel', 'custom')),
        json_schema NVARCHAR(MAX) NULL, -- For SurveyJS forms
        excel_structure NVARCHAR(MAX) NULL, -- For Excel-based forms
        general_department NVARCHAR(255) NOT NULL,
        next_department NVARCHAR(255) NOT NULL,
        direct_department NVARCHAR(255) NOT NULL,
        created_by UNIQUEIDENTIFIER NOT NULL,
        is_active BIT DEFAULT 1,
        version INT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (folder_id) REFERENCES folders(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_forms_folder ON forms(folder_id);
    CREATE INDEX IX_forms_department ON forms(general_department, next_department, direct_department);
    CREATE INDEX IX_forms_created_by ON forms(created_by);
    CREATE INDEX IX_forms_type ON forms(form_type);
    
    PRINT 'Table [forms] created successfully';
END
GO

-- =============================================
-- 5. FORM SUBMISSIONS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='form_submissions' AND xtype='U')
BEGIN
    CREATE TABLE form_submissions (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        form_id UNIQUEIDENTIFIER NOT NULL,
        user_id UNIQUEIDENTIFIER NOT NULL,
        submission_data NVARCHAR(MAX) NOT NULL, -- JSON data
        status NVARCHAR(50) DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'approved', 'rejected', 'final_approved')),
        approval_notes NVARCHAR(MAX) NULL,
        approved_by UNIQUEIDENTIFIER NULL,
        approved_at DATETIME2 NULL,
        final_approved_by UNIQUEIDENTIFIER NULL,
        final_approved_at DATETIME2 NULL,
        latitude DECIMAL(10, 8) NULL,
        longitude DECIMAL(11, 8) NULL,
        location_name NVARCHAR(255) NULL,
        time_spent INT NULL, -- in seconds
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (form_id) REFERENCES forms(id),
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (approved_by) REFERENCES users(id),
        FOREIGN KEY (final_approved_by) REFERENCES users(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_submissions_form ON form_submissions(form_id);
    CREATE INDEX IX_submissions_user ON form_submissions(user_id);
    CREATE INDEX IX_submissions_status ON form_submissions(status);
    CREATE INDEX IX_submissions_created ON form_submissions(created_at);
    CREATE INDEX IX_submissions_location ON form_submissions(latitude, longitude);
    
    PRINT 'Table [form_submissions] created successfully';
END
GO

-- =============================================
-- 6. SUBMISSION ATTACHMENTS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='submission_attachments' AND xtype='U')
BEGIN
    CREATE TABLE submission_attachments (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        submission_id UNIQUEIDENTIFIER NOT NULL,
        file_name NVARCHAR(255) NOT NULL,
        file_path NVARCHAR(500) NOT NULL,
        file_size BIGINT NOT NULL,
        file_type NVARCHAR(100) NOT NULL,
        mime_type NVARCHAR(100) NOT NULL,
        uploaded_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (submission_id) REFERENCES form_submissions(id) ON DELETE CASCADE
    );
    
    -- Create indexes
    CREATE INDEX IX_attachments_submission ON submission_attachments(submission_id);
    CREATE INDEX IX_attachments_type ON submission_attachments(file_type);
    
    PRINT 'Table [submission_attachments] created successfully';
END
GO

-- =============================================
-- 7. AUDIT LOG TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='audit_logs' AND xtype='U')
BEGIN
    CREATE TABLE audit_logs (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER NULL,
        action NVARCHAR(100) NOT NULL,
        table_name NVARCHAR(100) NOT NULL,
        record_id UNIQUEIDENTIFIER NULL,
        old_values NVARCHAR(MAX) NULL, -- JSON
        new_values NVARCHAR(MAX) NULL, -- JSON
        ip_address NVARCHAR(45) NULL,
        user_agent NVARCHAR(500) NULL,
        created_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_audit_user ON audit_logs(user_id);
    CREATE INDEX IX_audit_action ON audit_logs(action);
    CREATE INDEX IX_audit_table ON audit_logs(table_name);
    CREATE INDEX IX_audit_created ON audit_logs(created_at);
    
    PRINT 'Table [audit_logs] created successfully';
END
GO

-- =============================================
-- 8. SESSIONS TABLE (for user sessions)
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_sessions' AND xtype='U')
BEGIN
    CREATE TABLE user_sessions (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER NOT NULL,
        session_token NVARCHAR(255) NOT NULL UNIQUE,
        refresh_token NVARCHAR(255) NULL,
        ip_address NVARCHAR(45) NULL,
        user_agent NVARCHAR(500) NULL,
        is_active BIT DEFAULT 1,
        expires_at DATETIME2 NOT NULL,
        created_at DATETIME2 DEFAULT GETDATE(),
        last_accessed DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );
    
    -- Create indexes
    CREATE INDEX IX_sessions_user ON user_sessions(user_id);
    CREATE INDEX IX_sessions_token ON user_sessions(session_token);
    CREATE INDEX IX_sessions_expires ON user_sessions(expires_at);
    
    PRINT 'Table [user_sessions] created successfully';
END
GO

-- =============================================
-- 9. SYSTEM SETTINGS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_settings' AND xtype='U')
BEGIN
    CREATE TABLE system_settings (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        setting_key NVARCHAR(100) NOT NULL UNIQUE,
        setting_value NVARCHAR(MAX) NULL,
        setting_type NVARCHAR(50) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
        description NVARCHAR(500) NULL,
        is_public BIT DEFAULT 0, -- Whether setting can be accessed by frontend
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
    
    -- Create indexes
    CREATE INDEX IX_settings_key ON system_settings(setting_key);
    CREATE INDEX IX_settings_public ON system_settings(is_public);
    
    PRINT 'Table [system_settings] created successfully';
END
GO

-- =============================================
-- 10. NOTIFICATIONS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='notifications' AND xtype='U')
BEGIN
    CREATE TABLE notifications (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER NOT NULL,
        title NVARCHAR(255) NOT NULL,
        message NVARCHAR(MAX) NOT NULL,
        type NVARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
        related_id UNIQUEIDENTIFIER NULL, -- ID of related record (form, submission, etc.)
        related_type NVARCHAR(50) NULL, -- Type of related record
        is_read BIT DEFAULT 0,
        created_at DATETIME2 DEFAULT GETDATE(),
        read_at DATETIME2 NULL,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    );
    
    -- Create indexes
    CREATE INDEX IX_notifications_user ON notifications(user_id);
    CREATE INDEX IX_notifications_read ON notifications(is_read);
    CREATE INDEX IX_notifications_created ON notifications(created_at);
    
    PRINT 'Table [notifications] created successfully';
END
GO

-- =============================================
-- INSERT DEFAULT DATA
-- =============================================

-- Insert default admin user (password: admin123)
IF NOT EXISTS (SELECT * FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (username, email, password_hash, role, general_department, next_department, direct_department)
    VALUES ('admin', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'admin', 'Tổng Công Ty', 'Phòng IT', 'Team Development');
    
    PRINT 'Default admin user created';
END
GO

-- Insert default departments
IF NOT EXISTS (SELECT * FROM departments WHERE name = 'Tổng Công Ty')
BEGIN
    DECLARE @generalId UNIQUEIDENTIFIER = NEWID();
    DECLARE @itId UNIQUEIDENTIFIER = NEWID();
    DECLARE @productionId UNIQUEIDENTIFIER = NEWID();
    
    INSERT INTO departments (id, name, type, parent_id) VALUES 
    (@generalId, 'Tổng Công Ty', 'general', NULL),
    (@itId, 'Phòng IT', 'next', @generalId),
    (NEWID(), 'Team Development', 'direct', @itId),
    (NEWID(), 'Team Infrastructure', 'direct', @itId),
    (@productionId, 'Phòng Sản Xuất', 'next', @generalId),
    (NEWID(), 'Team QC', 'direct', @productionId),
    (NEWID(), 'Team Production', 'direct', @productionId);
    
    PRINT 'Default departments created';
END
GO

-- Insert default system settings
IF NOT EXISTS (SELECT * FROM system_settings WHERE setting_key = 'app_name')
BEGIN
    INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
    ('app_name', 'FieldReporter', 'string', 'Application name', 1),
    ('app_version', '1.0.0', 'string', 'Application version', 1),
    ('max_file_size', '10485760', 'number', 'Maximum file upload size in bytes (10MB)', 0),
    ('allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx","xls","xlsx"]', 'json', 'Allowed file types for upload', 0),
    ('session_timeout', '3600', 'number', 'Session timeout in seconds (1 hour)', 0),
    ('enable_notifications', 'true', 'boolean', 'Enable system notifications', 1);
    
    PRINT 'Default system settings created';
END
GO

-- =============================================
-- CREATE STORED PROCEDURES
-- =============================================

-- Procedure to get user permissions
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetUserPermissions')
    DROP PROCEDURE sp_GetUserPermissions;
GO

CREATE PROCEDURE sp_GetUserPermissions
    @UserId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.id,
        u.username,
        u.email,
        u.role,
        u.general_department,
        u.next_department,
        u.direct_department,
        u.is_active,
        CASE u.role
            WHEN 'admin' THEN 'create_folder,create_form,perform_checksheet,submit_checksheet,first_approval,final_approval,save_final_approved_checksheet,view_all_next_department_content,create_user'
            WHEN 'final_approval' THEN 'final_approval,save_final_approved_checksheet,view_all_next_department_content,create_user'
            WHEN 'approval' THEN 'first_approval,create_folder,create_form,save_final_approved_checksheet'
            WHEN 'submit' THEN 'create_folder,create_form,perform_checksheet,submit_checksheet,save_final_approved_checksheet'
            ELSE ''
        END as permissions
    FROM users u
    WHERE u.id = @UserId AND u.is_active = 1;
END
GO

-- Procedure to get accessible forms for user
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAccessibleForms')
    DROP PROCEDURE sp_GetAccessibleForms;
GO

CREATE PROCEDURE sp_GetAccessibleForms
    @UserId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserRole NVARCHAR(50);
    DECLARE @GeneralDept NVARCHAR(255);
    DECLARE @NextDept NVARCHAR(255);
    DECLARE @DirectDept NVARCHAR(255);
    
    SELECT @UserRole = role, @GeneralDept = general_department, 
           @NextDept = next_department, @DirectDept = direct_department
    FROM users WHERE id = @UserId;
    
    SELECT f.*, u.username as created_by_name
    FROM forms f
    INNER JOIN users u ON f.created_by = u.id
    WHERE f.is_active = 1
    AND (
        @UserRole = 'admin' OR
        (@UserRole = 'final_approval' AND f.general_department = @GeneralDept AND f.next_department = @NextDept) OR
        (f.general_department = @GeneralDept AND f.next_department = @NextDept AND f.direct_department = @DirectDept)
    )
    ORDER BY f.created_at DESC;
END
GO

-- =============================================
-- CREATE TRIGGERS FOR AUDIT LOGGING
-- =============================================

-- Trigger for users table
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_users_audit')
    DROP TRIGGER tr_users_audit;
GO

CREATE TRIGGER tr_users_audit
ON users
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Handle INSERT
    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values)
        SELECT i.id, 'INSERT', 'users', i.id, 
               (SELECT * FROM inserted i2 WHERE i2.id = i.id FOR JSON AUTO)
        FROM inserted i;
    END
    
    -- Handle UPDATE
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values)
        SELECT i.id, 'UPDATE', 'users', i.id,
               (SELECT * FROM deleted d WHERE d.id = i.id FOR JSON AUTO),
               (SELECT * FROM inserted i2 WHERE i2.id = i.id FOR JSON AUTO)
        FROM inserted i;
    END
    
    -- Handle DELETE
    IF NOT EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values)
        SELECT d.id, 'DELETE', 'users', d.id,
               (SELECT * FROM deleted d2 WHERE d2.id = d.id FOR JSON AUTO)
        FROM deleted d;
    END
END
GO

PRINT '==============================================';
PRINT 'FieldReporter Database Schema Created Successfully!';
PRINT '==============================================';
PRINT 'Tables created:';
PRINT '- users';
PRINT '- departments'; 
PRINT '- folders';
PRINT '- forms';
PRINT '- form_submissions';
PRINT '- submission_attachments';
PRINT '- audit_logs';
PRINT '- user_sessions';
PRINT '- system_settings';
PRINT '- notifications';
PRINT '';
PRINT 'Stored procedures created:';
PRINT '- sp_GetUserPermissions';
PRINT '- sp_GetAccessibleForms';
PRINT '';
PRINT 'Default data inserted:';
PRINT '- Admin user: <EMAIL>';
PRINT '- Default departments';
PRINT '- System settings';
PRINT '==============================================';