# FieldReporter - Modern Field Reporting Platform

## 📋 Overview
FieldReporter is a comprehensive field reporting platform that supports both traditional survey forms and Excel-based forms with real-time calculations. The application features role-based access control, department hierarchy management, and offline capabilities.

## 🏗️ Project Structure

```
FieldReporter/
├── backend/                    # Node.js/Express API Server
│   ├── src/
│   │   ├── config/            # Database & app configuration
│   │   ├── controllers/       # API route handlers
│   │   ├── middleware/        # Auth, validation, etc.
│   │   ├── routes/           # API route definitions
│   │   └── server.js         # Main server file
│   ├── database/             # SQL Server database scripts
│   │   ├── create_tables.sql # Database schema
│   │   ├── sample_data.sql   # Sample data
│   │   └── README.md         # Database documentation
│   ├── .env.example          # Environment variables template
│   ├── package.json          # Backend dependencies
│   └── README.md             # Backend documentation
│
├── frontend/                  # React/TypeScript Frontend
│   ├── src/
│   │   ├── components/       # React components
│   │   ├── pages/           # Page components
│   │   ├── contexts/        # React contexts
│   │   ├── services/        # API services
│   │   ├── config/          # Frontend configuration
│   │   ├── types/           # TypeScript types
│   │   └── utils/           # Utility functions
│   ├── public/              # Static assets
│   ├── index.html           # HTML template
│   ├── package.json         # Frontend dependencies
│   ├── vite.config.ts       # Vite configuration
│   ├── tailwind.config.js   # Tailwind CSS config
│   └── tsconfig.json        # TypeScript config
│
├── supabase/                 # Database migrations (legacy)
└── README.md                 # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- SQL Server 2016+
- Git

### 1. Clone Repository
```bash
git clone <repository-url>
cd FieldReporter
```

### 2. Setup Database
```bash
# Run SQL Server Management Studio
# Execute: backend/database/create_tables.sql
# Execute: backend/database/sample_data.sql
```

### 3. Setup Backend
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your database credentials
npm run dev
```

### 4. Setup Frontend
```bash
cd frontend
npm install
cp .env.example .env
# Edit .env with API URL (default: http://localhost:5000/api)
npm run dev
```

### 5. Access Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000/api
- **Default Login**: <EMAIL> / admin123

## 🔧 Features

### Core Features
- **Multi-Form Support**: Survey forms + Excel forms with formulas
- **Role-Based Access**: 4 user roles with granular permissions
- **Department Hierarchy**: 3-level department structure
- **Workflow Management**: Draft → Submit → Approve → Final Approve
- **File Management**: Folder-based organization
- **GPS Location**: Location tracking for submissions
- **Offline Support**: PWA with service worker
- **Audit Trail**: Complete activity logging

### Excel Platform Features
- **Real-time Calculations**: HyperFormula integration
- **Merged Cells**: Full merge/unmerge support
- **Cell Types**: Text, Number, Date, Checkbox
- **Validation**: Min/Max limits with OK/NG indicators
- **Borders & Styling**: Professional formatting
- **Resize Columns/Rows**: Interactive sizing
- **Delete Columns/Rows**: Full editing capabilities

### Security Features
- **JWT Authentication**: Access + refresh tokens
- **Rate Limiting**: DDoS protection
- **Input Validation**: Comprehensive validation
- **SQL Injection Prevention**: Parameterized queries
- **Department Access Control**: Data isolation

## 👥 User Roles & Permissions

| Role | Permissions |
|------|-------------|
| **Admin** | Full system access |
| **Final Approval** | Department oversight + user creation |
| **Approval** | First-level approval within department |
| **Submit** | Form creation and submission |

## 🗄️ Database Schema

### Core Tables
- **users** - User accounts and authentication
- **departments** - 3-level department hierarchy
- **folders** - File system organization
- **forms** - Form definitions (Survey + Excel)
- **form_submissions** - Submission data + workflow
- **submission_attachments** - File attachments
- **audit_logs** - System audit trail
- **user_sessions** - Session management
- **system_settings** - Application configuration
- **notifications** - User notifications

## 🌐 API Endpoints

### Authentication
```
POST /api/auth/login          # User login
POST /api/auth/register       # User registration
POST /api/auth/logout         # User logout
GET  /api/auth/profile        # Get user profile
POST /api/auth/refresh-token  # Refresh JWT token
```

### Forms Management
```
GET    /api/forms             # Get all forms
GET    /api/forms/:id         # Get single form
POST   /api/forms             # Create new form
PUT    /api/forms/:id         # Update form
DELETE /api/forms/:id         # Delete form
```

### Folders Management
```
GET    /api/folders           # Get all folders
GET    /api/folders/:id       # Get single folder
POST   /api/folders           # Create new folder
PUT    /api/folders/:id       # Update folder
DELETE /api/folders/:id       # Delete folder
```

### Form Submissions
```
GET    /api/submissions       # Get all submissions
GET    /api/submissions/:id   # Get single submission
POST   /api/submissions       # Create submission
PATCH  /api/submissions/:id/status # Update status
DELETE /api/submissions/:id   # Delete submission
```

## 🔧 Development

### Backend Development
```bash
cd backend
npm run dev          # Start development server
npm test            # Run tests
npm run migrate     # Run database migrations
```

### Frontend Development
```bash
cd frontend
npm run dev         # Start development server
npm run build       # Build for production
npm run preview     # Preview production build
npm run lint        # Run ESLint
```

### Environment Variables

#### Backend (.env)
```env
DB_SERVER=localhost
DB_DATABASE=FieldReporter
DB_USER=your_username
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret
PORT=5000
```

#### Frontend (.env)
```env
VITE_API_BASE_URL=http://localhost:5000/api
VITE_APP_NAME=FieldReporter
```

## 📱 PWA Features
- **Offline Support**: Service worker caching
- **Install Prompt**: Add to home screen
- **Background Sync**: Offline form submissions
- **Push Notifications**: Real-time updates

## 🚀 Deployment

### Backend Deployment
```bash
cd backend
npm ci --production
npm start
```

### Frontend Deployment
```bash
cd frontend
npm run build
# Deploy dist/ folder to web server
```

### Docker Deployment
```bash
# Backend
docker build -t fieldreporter-backend ./backend
docker run -p 5000:5000 fieldreporter-backend

# Frontend
docker build -t fieldreporter-frontend ./frontend
docker run -p 80:80 fieldreporter-frontend
```

## 🧪 Testing

### Sample Users
- **Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / admin123
- **User**: <EMAIL> / admin123

### Test Data
The database includes sample:
- Department structure
- Forms (Survey + Excel)
- Submissions with different statuses
- User accounts with different roles

## 📚 Documentation
- **Backend API**: `/backend/README.md`
- **Database Schema**: `/backend/database/README.md`
- **Frontend Components**: `/frontend/src/components/`

## 🤝 Contributing
1. Fork the repository
2. Create feature branch
3. Make changes in appropriate folder (backend/frontend)
4. Test thoroughly
5. Submit pull request

## 📄 License
MIT License - see LICENSE file for details

## 🆘 Support
For issues and questions:
1. Check documentation in respective folders
2. Review error logs
3. Test API endpoints with Postman
4. Verify database connectivity
5. Check environment variables

---

**FieldReporter** - Modern field reporting made simple and powerful! 🚀