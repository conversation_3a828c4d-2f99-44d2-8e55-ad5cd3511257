const { v4: uuidv4 } = require('uuid');
const database = require('../config/database');

// Get all folders accessible to user
const getFolders = async (req, res) => {
  try {
    const user = req.user;
    const { parent_id } = req.query;

    let whereClause = 'WHERE fo.is_active = 1';
    let params = {};

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        whereClause += ' AND fo.general_department = @generalDepartment AND fo.next_department = @nextDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        whereClause += ' AND fo.general_department = @generalDepartment AND fo.next_department = @nextDepartment AND fo.direct_department = @directDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    // Parent folder filter
    if (parent_id) {
      if (parent_id === 'root') {
        where<PERSON>lause += ' AND fo.parent_id IS NULL';
      } else {
        whereClause += ' AND fo.parent_id = @parentId';
        params.parentId = parent_id;
      }
    }

    const query = `
      SELECT 
        fo.id, fo.name, fo.description, fo.parent_id,
        fo.general_department, fo.next_department, fo.direct_department,
        fo.created_at, fo.updated_at,
        u.username as created_by_name,
        pf.name as parent_folder_name,
        (SELECT COUNT(*) FROM folders cf WHERE cf.parent_id = fo.id AND cf.is_active = 1) as subfolder_count,
        (SELECT COUNT(*) FROM forms f WHERE f.folder_id = fo.id AND f.is_active = 1) as form_count
      FROM folders fo
      LEFT JOIN users u ON fo.created_by = u.id
      LEFT JOIN folders pf ON fo.parent_id = pf.id
      ${whereClause}
      ORDER BY fo.name ASC
    `;

    const result = await database.query(query, params);

    res.json({
      success: true,
      data: {
        folders: result.recordset
      }
    });

  } catch (error) {
    console.error('Get folders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get folders'
    });
  }
};

// Get single folder by ID
const getFolderById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    let whereClause = 'WHERE fo.id = @folderId AND fo.is_active = 1';
    let params = { folderId: id };

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        whereClause += ' AND fo.general_department = @generalDepartment AND fo.next_department = @nextDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        whereClause += ' AND fo.general_department = @generalDepartment AND fo.next_department = @nextDepartment AND fo.direct_department = @directDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    const query = `
      SELECT 
        fo.id, fo.name, fo.description, fo.parent_id,
        fo.general_department, fo.next_department, fo.direct_department,
        fo.created_at, fo.updated_at,
        u.username as created_by_name,
        pf.name as parent_folder_name
      FROM folders fo
      LEFT JOIN users u ON fo.created_by = u.id
      LEFT JOIN folders pf ON fo.parent_id = pf.id
      ${whereClause}
    `;

    const result = await database.query(query, params);

    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Folder not found or access denied'
      });
    }

    res.json({
      success: true,
      data: { folder: result.recordset[0] }
    });

  } catch (error) {
    console.error('Get folder by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get folder'
    });
  }
};

// Create new folder
const createFolder = async (req, res) => {
  try {
    const {
      name,
      description,
      parent_id
    } = req.body;

    const user = req.user;
    const folderId = uuidv4();

    // Validate parent folder access if parent_id provided
    if (parent_id) {
      const parentResult = await database.query(
        'SELECT id FROM folders WHERE id = @parentId AND general_department = @generalDepartment AND next_department = @nextDepartment AND direct_department = @directDepartment',
        {
          parentId: parent_id,
          generalDepartment: user.general_department,
          nextDepartment: user.next_department,
          directDepartment: user.direct_department
        }
      );

      if (parentResult.recordset.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Parent folder not found or access denied'
        });
      }
    }

    // Check for duplicate folder name in same parent
    const duplicateCheck = await database.query(`
      SELECT id FROM folders 
      WHERE name = @name 
        AND parent_id ${parent_id ? '= @parentId' : 'IS NULL'}
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
        AND is_active = 1
    `, {
      name,
      parentId: parent_id || null,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (duplicateCheck.recordset.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Folder with this name already exists in the same location'
      });
    }

    // Create folder
    await database.query(`
      INSERT INTO folders (
        id, name, description, parent_id,
        general_department, next_department, direct_department,
        created_by
      ) VALUES (
        @folderId, @name, @description, @parentId,
        @generalDepartment, @nextDepartment, @directDepartment,
        @createdBy
      )
    `, {
      folderId,
      name,
      description,
      parentId: parent_id || null,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department,
      createdBy: user.id
    });

    // Get created folder
    const folderResult = await database.query(
      'SELECT id, name, description, parent_id, created_at FROM folders WHERE id = @folderId',
      { folderId }
    );

    res.status(201).json({
      success: true,
      message: 'Folder created successfully',
      data: { folder: folderResult.recordset[0] }
    });

  } catch (error) {
    console.error('Create folder error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create folder'
    });
  }
};

// Update folder
const updateFolder = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    const user = req.user;

    // Check if folder exists and user has access
    const existingFolder = await database.query(`
      SELECT id, created_by, name as current_name, parent_id
      FROM folders 
      WHERE id = @folderId 
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
        AND is_active = 1
    `, {
      folderId: id,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (existingFolder.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Folder not found or access denied'
      });
    }

    // Check if user can edit (creator or admin)
    if (user.role !== 'admin' && existingFolder.recordset[0].created_by !== user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only edit folders you created'
      });
    }

    // Check for duplicate name if name is being changed
    if (name && name !== existingFolder.recordset[0].current_name) {
      const duplicateCheck = await database.query(`
        SELECT id FROM folders 
        WHERE name = @name 
          AND parent_id ${existingFolder.recordset[0].parent_id ? '= @parentId' : 'IS NULL'}
          AND general_department = @generalDepartment 
          AND next_department = @nextDepartment 
          AND direct_department = @directDepartment
          AND is_active = 1
          AND id != @folderId
      `, {
        name,
        parentId: existingFolder.recordset[0].parent_id,
        generalDepartment: user.general_department,
        nextDepartment: user.next_department,
        directDepartment: user.direct_department,
        folderId: id
      });

      if (duplicateCheck.recordset.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Folder with this name already exists in the same location'
        });
      }
    }

    // Update folder
    const updateFields = [];
    const params = { folderId: id };

    if (name !== undefined) {
      updateFields.push('name = @name');
      params.name = name;
    }

    if (description !== undefined) {
      updateFields.push('description = @description');
      params.description = description;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    updateFields.push('updated_at = GETDATE()');

    const updateQuery = `
      UPDATE folders 
      SET ${updateFields.join(', ')}
      WHERE id = @folderId
    `;

    await database.query(updateQuery, params);

    // Get updated folder
    const updatedFolder = await database.query(
      'SELECT id, name, description, parent_id, updated_at FROM folders WHERE id = @folderId',
      { folderId: id }
    );

    res.json({
      success: true,
      message: 'Folder updated successfully',
      data: { folder: updatedFolder.recordset[0] }
    });

  } catch (error) {
    console.error('Update folder error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update folder'
    });
  }
};

// Delete folder
const deleteFolder = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Check if folder exists and user has access
    const existingFolder = await database.query(`
      SELECT id, created_by, name
      FROM folders 
      WHERE id = @folderId 
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
        AND is_active = 1
    `, {
      folderId: id,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (existingFolder.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Folder not found or access denied'
      });
    }

    // Check if user can delete (creator or admin)
    if (user.role !== 'admin' && existingFolder.recordset[0].created_by !== user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete folders you created'
      });
    }

    // Check if folder has subfolders or forms
    const contentCheck = await database.query(`
      SELECT 
        (SELECT COUNT(*) FROM folders WHERE parent_id = @folderId AND is_active = 1) as subfolder_count,
        (SELECT COUNT(*) FROM forms WHERE folder_id = @folderId AND is_active = 1) as form_count
    `, { folderId: id });

    const { subfolder_count, form_count } = contentCheck.recordset[0];

    if (subfolder_count > 0 || form_count > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete folder. It contains ${subfolder_count} subfolders and ${form_count} forms. Please move or delete the contents first.`
      });
    }

    // Soft delete folder
    await database.query(
      'UPDATE folders SET is_active = 0, updated_at = GETDATE() WHERE id = @folderId',
      { folderId: id }
    );

    res.json({
      success: true,
      message: 'Folder deleted successfully'
    });

  } catch (error) {
    console.error('Delete folder error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete folder'
    });
  }
};

// Get folder breadcrumb path
const getFolderPath = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    const path = [];
    let currentId = id;

    // Build path from current folder to root
    while (currentId) {
      const folderResult = await database.query(`
        SELECT id, name, parent_id
        FROM folders 
        WHERE id = @folderId 
          AND general_department = @generalDepartment 
          AND next_department = @nextDepartment 
          AND direct_department = @directDepartment
          AND is_active = 1
      `, {
        folderId: currentId,
        generalDepartment: user.general_department,
        nextDepartment: user.next_department,
        directDepartment: user.direct_department
      });

      if (folderResult.recordset.length === 0) {
        break;
      }

      const folder = folderResult.recordset[0];
      path.unshift({
        id: folder.id,
        name: folder.name
      });

      currentId = folder.parent_id;
    }

    // Add root
    path.unshift({
      id: null,
      name: 'Root'
    });

    res.json({
      success: true,
      data: { path }
    });

  } catch (error) {
    console.error('Get folder path error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get folder path'
    });
  }
};

module.exports = {
  getFolders,
  getFolderById,
  createFolder,
  updateFolder,
  deleteFolder,
  getFolderPath
};