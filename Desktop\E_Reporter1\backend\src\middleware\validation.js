const { body, param, query, validationResult } = require('express-validator');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// User validation rules
const validateUserRegistration = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  
  body('role')
    .isIn(['admin', 'submit', 'approval', 'final_approval'])
    .withMessage('Invalid role'),
  
  body('general_department')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('General department is required'),
  
  body('next_department')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Next department is required'),
  
  body('direct_department')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Direct department is required'),
  
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  
  handleValidationErrors
];

// Form validation rules
const validateFormCreation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Form name is required and must be less than 255 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  
  body('form_type')
    .isIn(['survey', 'excel', 'custom'])
    .withMessage('Invalid form type'),
  
  body('folder_id')
    .optional()
    .isUUID()
    .withMessage('Invalid folder ID'),
  
  body('json_schema')
    .optional()
    .custom((value) => {
      if (value) {
        try {
          JSON.parse(value);
          return true;
        } catch (error) {
          throw new Error('Invalid JSON schema');
        }
      }
      return true;
    }),
  
  body('excel_structure')
    .optional()
    .custom((value) => {
      if (value) {
        try {
          JSON.parse(value);
          return true;
        } catch (error) {
          throw new Error('Invalid Excel structure');
        }
      }
      return true;
    }),
  
  handleValidationErrors
];

// Form submission validation
const validateFormSubmission = [
  body('form_id')
    .isUUID()
    .withMessage('Valid form ID is required'),
  
  body('submission_data')
    .custom((value) => {
      try {
        JSON.parse(value);
        return true;
      } catch (error) {
        throw new Error('Invalid submission data JSON');
      }
    }),
  
  body('latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Invalid latitude'),
  
  body('longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Invalid longitude'),
  
  body('location_name')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Location name must be less than 255 characters'),
  
  handleValidationErrors
];

// Folder validation rules
const validateFolderCreation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Folder name is required and must be less than 255 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  
  body('parent_id')
    .optional()
    .isUUID()
    .withMessage('Invalid parent folder ID'),
  
  handleValidationErrors
];

// Checksheet validation rules
const validateChecksheetSession = [
  body('form_id')
    .isUUID()
    .withMessage('Valid form ID is required'),
  
  body('session_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Session name is required and must be less than 255 characters'),
  
  body('latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Invalid latitude'),
  
  body('longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Invalid longitude'),
  
  body('location_name')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Location name must be less than 255 characters'),
  
  handleValidationErrors
];

const validateChecksheetResponse = [
  body('question_id')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Question ID is required'),
  
  body('question_text')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Question text is required'),
  
  body('question_type')
    .isIn(['text', 'textarea', 'radio', 'checkbox', 'dropdown', 'rating', 'date'])
    .withMessage('Invalid question type'),
  
  body('response_value')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Response value too long'),
  
  body('is_required')
    .optional()
    .isBoolean()
    .withMessage('is_required must be boolean'),
  
  handleValidationErrors
];

// UUID parameter validation
const validateUUID = (paramName) => [
  param(paramName)
    .isUUID()
    .withMessage(`Invalid ${paramName}`),
  
  handleValidationErrors
];

// Pagination validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  handleValidationErrors
];

module.exports = {
  validateUserRegistration,
  validateUserLogin,
  validateFormCreation,
  validateFormSubmission,
  validateFolderCreation,
  validateChecksheetSession,
  validateChecksheetResponse,
  validateUUID,
  validatePagination,
  handleValidationErrors
};