const jwt = require('jsonwebtoken');
const database = require('../config/database');

// Verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const result = await database.query(
      'SELECT id, username, email, role, general_department, next_department, direct_department, is_active FROM users WHERE id = @userId',
      { userId: decoded.userId }
    );

    if (result.recordset.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = result.recordset[0];
    
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is inactive'
      });
    }

    // Add user to request
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }

    console.error('Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// Check user permissions
const requirePermission = (permission) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Admin has all permissions
    if (user.role === 'admin') {
      return next();
    }

    // Define role permissions
    const rolePermissions = {
      submit: [
        'create_folder',
        'create_form', 
        'perform_checksheet',
        'submit_checksheet',
        'save_final_approved_checksheet'
      ],
      approval: [
        'first_approval',
        'create_folder',
        'create_form',
        'save_final_approved_checksheet'
      ],
      final_approval: [
        'final_approval',
        'save_final_approved_checksheet',
        'view_all_next_department_content',
        'create_user'
      ]
    };

    const userPermissions = rolePermissions[user.role] || [];
    
    if (!userPermissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Check department access
const requireDepartmentAccess = (req, res, next) => {
  const user = req.user;
  const { general_department, next_department, direct_department } = req.body;

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Admin can access all departments
  if (user.role === 'admin') {
    return next();
  }

  // Final approval can access all content in their next_department level
  if (user.role === 'final_approval') {
    if (general_department === user.general_department && 
        next_department === user.next_department) {
      return next();
    }
  }

  // Other roles can only access their exact department flow
  if (general_department === user.general_department &&
      next_department === user.next_department &&
      direct_department === user.direct_department) {
    return next();
  }

  return res.status(403).json({
    success: false,
    message: 'Department access denied'
  });
};

module.exports = {
  authenticateToken,
  requirePermission,
  requireDepartmentAccess
};