import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  FileText, 
  Table, 
  Settings, 
  Save, 
  Eye,
  Plus,
  Trash2,
  GripVertical
} from 'lucide-react';

export function CreateForm() {
  const navigate = useNavigate();
  const [formType, setFormType] = useState<'survey' | 'excel'>('survey');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    folder_id: '',
    category: 'general'
  });

  const [surveyElements, setSurveyElements] = useState([
    {
      id: '1',
      type: 'text',
      title: 'Sample Text Field',
      required: false,
      placeholder: 'Enter text here...'
    }
  ]);

  const elementTypes = [
    { value: 'text', label: 'Text Input', icon: FileText },
    { value: 'textarea', label: 'Text Area', icon: FileText },
    { value: 'radio', label: 'Radio Buttons', icon: Settings },
    { value: 'checkbox', label: 'Checkboxes', icon: Settings },
    { value: 'dropdown', label: 'Dropdown', icon: Settings },
    { value: 'rating', label: 'Rating Scale', icon: Settings }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const addElement = (type: string) => {
    const newElement = {
      id: Date.now().toString(),
      type,
      title: `New ${type} field`,
      required: false,
      placeholder: type === 'text' ? 'Enter text...' : ''
    };
    setSurveyElements([...surveyElements, newElement]);
  };

  const removeElement = (id: string) => {
    setSurveyElements(surveyElements.filter(el => el.id !== id));
  };

  const updateElement = (id: string, field: string, value: any) => {
    setSurveyElements(surveyElements.map(el => 
      el.id === id ? { ...el, [field]: value } : el
    ));
  };

  const handleSave = () => {
    console.log('Saving form:', { formData, formType, surveyElements });
    // TODO: Implement actual save functionality
    navigate('/forms');
  };

  const handlePreview = () => {
    console.log('Previewing form:', { formData, formType, surveyElements });
    // TODO: Implement preview functionality
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/forms')}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Form</h1>
            <p className="text-gray-600 mt-1">Build your custom form with drag-and-drop elements</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handlePreview}
            className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </button>
          <button
            onClick={handleSave}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Form
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Form Settings Sidebar */}
        <div className="lg:col-span-1 space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Form Settings</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Form Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter form name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe your form"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Form Type
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => setFormType('survey')}
                    className={`p-3 border-2 rounded-lg text-center transition-colors ${
                      formType === 'survey'
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <FileText className="w-5 h-5 mx-auto mb-1" />
                    <span className="text-xs font-medium">Survey</span>
                  </button>
                  <button
                    onClick={() => setFormType('excel')}
                    className={`p-3 border-2 rounded-lg text-center transition-colors ${
                      formType === 'excel'
                        ? 'border-green-500 bg-green-50 text-green-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Table className="w-5 h-5 mx-auto mb-1" />
                    <span className="text-xs font-medium">Excel</span>
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="general">General</option>
                  <option value="safety">Safety</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="quality">Quality Control</option>
                  <option value="hr">Human Resources</option>
                </select>
              </div>
            </div>
          </div>

          {/* Element Palette */}
          {formType === 'survey' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Elements</h3>
              <div className="space-y-2">
                {elementTypes.map((element) => {
                  const Icon = element.icon;
                  return (
                    <button
                      key={element.value}
                      onClick={() => addElement(element.value)}
                      className="w-full flex items-center p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                    >
                      <Icon className="w-4 h-4 mr-3 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">
                        {element.label}
                      </span>
                      <Plus className="w-4 h-4 ml-auto text-gray-400" />
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Form Builder */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {formType === 'survey' ? 'Form Builder' : 'Excel Template Designer'}
              </h3>
            </div>

            <div className="p-6">
              {formType === 'survey' ? (
                <div className="space-y-4">
                  {surveyElements.map((element, index) => (
                    <div
                      key={element.id}
                      className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
                          <span className="text-sm font-medium text-gray-700 capitalize">
                            {element.type} Field
                          </span>
                        </div>
                        <button
                          onClick={() => removeElement(element.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-1">
                            Field Title
                          </label>
                          <input
                            type="text"
                            value={element.title}
                            onChange={(e) => updateElement(element.id, 'title', e.target.value)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        {element.type === 'text' && (
                          <div>
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              Placeholder
                            </label>
                            <input
                              type="text"
                              value={element.placeholder}
                              onChange={(e) => updateElement(element.id, 'placeholder', e.target.value)}
                              className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        )}

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`required-${element.id}`}
                            checked={element.required}
                            onChange={(e) => updateElement(element.id, 'required', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`required-${element.id}`} className="ml-2 text-xs text-gray-600">
                            Required field
                          </label>
                        </div>

                        {/* Preview of the element */}
                        <div className="pt-2 border-t border-gray-100">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {element.title}
                            {element.required && <span className="text-red-500 ml-1">*</span>}
                          </label>
                          {element.type === 'text' && (
                            <input
                              type="text"
                              placeholder={element.placeholder}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                              disabled
                            />
                          )}
                          {element.type === 'textarea' && (
                            <textarea
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                              disabled
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

                  {surveyElements.length === 0 && (
                    <div className="text-center py-12 text-gray-500">
                      <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium mb-2">Start building your form</p>
                      <p className="text-sm">Add elements from the sidebar to get started</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Table className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium mb-2">Excel Template Designer</p>
                  <p className="text-sm">Excel form builder coming soon</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}