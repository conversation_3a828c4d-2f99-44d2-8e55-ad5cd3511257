import { apiClient } from '../config/api';

export interface Form {
  id: string;
  name: string;
  description: string;
  type: 'survey' | 'excel';
  folder: string;
  created_by: string;
  created_at: string;
  submissions: number;
  status: string;
}

export interface GetFormsResponse {
  success: boolean;
  data: {
    forms: Form[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    }
  }
}

export const formsService = {
  async getForms(params?: { 
    page?: number;
    limit?: number;
    folder_id?: string;
    search?: string;
  }): Promise<GetFormsResponse> {
    const response = await apiClient.get('/forms', { params });
    return response.data;
  },

  async getFormById(id: string) {
    const response = await apiClient.get(`/forms/${id}`);
    return response.data;
  },

  async createForm(formData: Partial<Form>) {
    const response = await apiClient.post('/forms', formData);
    return response.data;
  },

  async updateForm(id: string, formData: Partial<Form>) {
    const response = await apiClient.put(`/forms/${id}`, formData);
    return response.data;
  },

  async deleteForm(id: string) {
    const response = await apiClient.delete(`/forms/${id}`);
    return response.data;
  }
};
