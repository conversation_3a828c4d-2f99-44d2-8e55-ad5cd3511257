import React, { useState } from 'react';
import { BarChart3, Download, Filter, Calendar, FileText, Users, TrendingUp, Eye } from 'lucide-react';

export function Reports() {
  const [selectedReport, setSelectedReport] = useState('submissions');
  const [dateRange, setDateRange] = useState('last30days');

  const reportTypes = [
    { id: 'submissions', name: 'Form Submissions', icon: FileText },
    { id: 'users', name: 'User Activity', icon: Users },
    { id: 'performance', name: 'Performance Metrics', icon: TrendingUp },
    { id: 'departments', name: 'Department Analytics', icon: BarChart3 }
  ];

  const mockReportData = {
    submissions: {
      total: 1247,
      thisMonth: 89,
      change: '+12%',
      data: [
        { name: 'Employee Onboarding', submissions: 45, completion: '92%' },
        { name: 'Leave Request', submissions: 32, completion: '98%' },
        { name: 'Expense Report', submissions: 28, completion: '85%' },
        { name: 'Performance Review', submissions: 15, completion: '76%' }
      ]
    },
    users: {
      total: 156,
      active: 134,
      change: '+5%',
      data: [
        { name: 'HR Department', users: 12, active: 11 },
        { name: 'Engineering', users: 45, active: 42 },
        { name: 'Sales', users: 23, active: 21 },
        { name: 'Marketing', users: 18, active: 16 }
      ]
    }
  };

  const currentData = mockReportData[selectedReport as keyof typeof mockReportData] || mockReportData.submissions;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-1">View detailed insights and generate reports</p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Report Type Selection */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {reportTypes.map((type) => {
          const Icon = type.icon;
          return (
            <button
              key={type.id}
              onClick={() => setSelectedReport(type.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedReport === type.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
              }`}
            >
              <Icon className="w-6 h-6 mx-auto mb-2" />
              <p className="font-medium text-sm">{type.name}</p>
            </button>
          );
        })}
      </div>

      {/* Date Range Selector */}
      <div className="flex items-center space-x-4 bg-white p-4 rounded-lg border border-gray-200">
        <Calendar className="w-5 h-5 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">Date Range:</span>
        <select
          value={dateRange}
          onChange={(e) => setDateRange(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="last7days">Last 7 days</option>
          <option value="last30days">Last 30 days</option>
          <option value="last90days">Last 90 days</option>
          <option value="lastyear">Last year</option>
          <option value="custom">Custom range</option>
        </select>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total {selectedReport === 'submissions' ? 'Submissions' : 'Users'}</p>
              <p className="text-2xl font-bold text-gray-900">{currentData.total}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-green-600 text-sm font-medium">{currentData.change}</span>
            <span className="text-gray-600 text-sm ml-2">from last period</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-900">{selectedReport === 'submissions' ? currentData.thisMonth : currentData.active}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-blue-600 text-sm font-medium">+8.2%</span>
            <span className="text-gray-600 text-sm ml-2">from last month</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-2xl font-bold text-gray-900">89.5%</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-green-600 text-sm font-medium">+2.1%</span>
            <span className="text-gray-600 text-sm ml-2">improvement</span>
          </div>
        </div>
      </div>

      {/* Detailed Report Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {reportTypes.find(t => t.id === selectedReport)?.name} Details
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {selectedReport === 'submissions' ? 'Submissions' : 'Users'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {selectedReport === 'submissions' ? 'Completion Rate' : 'Active'}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentData.data.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{item.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {selectedReport === 'submissions' ? item.submissions : item.users}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {selectedReport === 'submissions' ? item.completion : item.active}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 flex items-center">
                      <Eye className="w-4 h-4 mr-1" />
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Chart Placeholder */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Trends Over Time</h3>
        <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Chart visualization will be displayed here</p>
            <p className="text-sm text-gray-400 mt-1">Integration with charting library needed</p>
          </div>
        </div>
      </div>
    </div>
  );
}