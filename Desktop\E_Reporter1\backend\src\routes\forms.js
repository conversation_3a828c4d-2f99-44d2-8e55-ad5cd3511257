const express = require('express');
const router = express.Router();
const formsController = require('../controllers/formsController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { validateFormCreation, validateUUID, validatePagination } = require('../middleware/validation');

// Get all forms
router.get('/', 
  authenticateToken, 
  validatePagination,
  formsController.getForms
);

// Get single form
router.get('/:id', 
  authenticateToken, 
  validateUUID('id'),
  formsController.getFormById
);

// Create new form
router.post('/', 
  authenticateToken, 
  requirePermission('create_form'),
  validateFormCreation,
  formsController.createForm
);

// Update form
router.put('/:id', 
  authenticateToken, 
  requirePermission('create_form'),
  validateUUID('id'),
  formsController.updateForm
);

// Delete form
router.delete('/:id', 
  authenticateToken, 
  requirePermission('create_form'),
  validateUUID('id'),
  formsController.deleteForm
);

module.exports = router;