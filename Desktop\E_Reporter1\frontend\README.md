# FieldReporter Frontend

## 📋 Overview
Modern React/TypeScript frontend for the FieldReporter application with comprehensive form management and Excel integration.

## ✨ Features
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Vite** for fast development
- **PWA Support** with offline capabilities
- **Excel Platform** with HyperFormula integration
- **Role-based UI** with permission system
- **Responsive Design** for all devices
- **Real-time Form Builder** with drag & drop

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- Backend API running on port 5000

### Installation
```bash
cd frontend
npm install
```

### Environment Setup
```bash
cp .env.example .env
# Edit .env with your API URL
```

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

## 🏗️ Project Structure

```
frontend/
├── src/
│   ├── components/          # Reusable components
│   │   ├── Auth/           # Authentication components
│   │   ├── Dashboard/      # Dashboard widgets
│   │   ├── Forms/          # Form-related components
│   │   └── Layout/         # Layout components
│   ├── pages/              # Page components
│   │   ├── Dashboard.tsx   # Main dashboard
│   │   ├── Forms.tsx       # Forms management
│   │   ├── Reports.tsx     # Reports & analytics
│   │   └── CreateForm.tsx  # Form creation
│   ├── contexts/           # React contexts
│   │   └── AuthContext.tsx # Authentication context
│   ├── services/           # API services
│   │   ├── authService.ts  # Authentication API
│   │   ├── formsService.ts # Forms API
│   │   └── foldersService.ts # Folders API
│   ├── config/             # Configuration
│   │   └── api.ts          # API client setup
│   ├── types/              # TypeScript types
│   │   └── index.ts        # Type definitions
│   └── utils/              # Utility functions
├── public/                 # Static assets
│   ├── manifest.json       # PWA manifest
│   ├── sw.js              # Service worker
│   └── icons/             # App icons
├── index.html             # HTML template
├── vite.config.ts         # Vite configuration
├── tailwind.config.js     # Tailwind CSS config
└── tsconfig.json          # TypeScript config
```

## 🎨 Key Components

### Excel Platform
- **ExcelPlatform.tsx** - Main Excel editor
- **ExcelPreview.tsx** - Form preview with calculations
- **ExcelFormBuilder.tsx** - Form builder interface

### Authentication
- **LoginForm.tsx** - User login
- **RegisterForm.tsx** - User registration
- **AuthContext.tsx** - Authentication state management

### Forms Management
- **FormsList.tsx** - Forms listing and management
- **SurveyForm.tsx** - Survey form renderer
- **CreateForm.tsx** - Form creation wizard

### Dashboard
- **StatsCards.tsx** - Statistics widgets
- **RecentActivity.tsx** - Activity feed
- **Dashboard.tsx** - Main dashboard page

## 🔧 Configuration

### Environment Variables
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api

# App Configuration
VITE_APP_NAME=FieldReporter
VITE_APP_VERSION=1.0.0

# File Upload Settings
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
```

### API Integration
The frontend uses a centralized API client with:
- **Automatic token refresh**
- **Request/response interceptors**
- **Error handling**
- **Type safety**

```typescript
// Example API usage
import { formsService } from '../services/formsService';

const forms = await formsService.getForms({
  page: 1,
  limit: 10,
  folder_id: 'folder-id'
});
```

## 🎯 User Roles & Permissions

The frontend adapts UI based on user permissions:

```typescript
const { hasPermission } = useAuth();

// Conditional rendering based on permissions
{hasPermission('create_form') && (
  <button>Create Form</button>
)}
```

### Permission Matrix
- **create_folder** - Create folders
- **create_form** - Create forms
- **perform_checksheet** - Fill out forms
- **submit_checksheet** - Submit forms
- **first_approval** - First-level approval
- **final_approval** - Final approval
- **create_user** - User management

## 📱 PWA Features

### Service Worker
- **Offline caching** for static assets
- **Background sync** for form submissions
- **Push notifications** support

### Manifest
- **Install prompt** for mobile devices
- **App icons** for different sizes
- **Splash screen** configuration

## 🎨 Styling & Theming

### Tailwind CSS
- **Utility-first** CSS framework
- **Responsive design** with breakpoints
- **Custom color palette** for branding
- **Dark mode** support (planned)

### Component Library
- **Lucide React** for icons
- **Headless UI** for accessible components
- **Custom components** with consistent styling

## 🧪 Testing

### Component Testing
```bash
npm run test         # Run tests
npm run test:watch   # Watch mode
npm run test:coverage # Coverage report
```

### E2E Testing
```bash
npm run e2e          # Run E2E tests
npm run e2e:ui       # Interactive mode
```

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Static Hosting
Deploy the `dist/` folder to:
- **Netlify**
- **Vercel**
- **AWS S3 + CloudFront**
- **Azure Static Web Apps**

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --production
COPY . .
RUN npm run build
EXPOSE 80
CMD ["npm", "run", "preview"]
```

## 🔍 Debugging

### Development Tools
- **React DevTools** for component inspection
- **Redux DevTools** for state management
- **Network tab** for API debugging
- **Console logs** for error tracking

### Common Issues
1. **API connection** - Check VITE_API_BASE_URL
2. **Authentication** - Verify JWT tokens
3. **Permissions** - Check user role and permissions
4. **CORS errors** - Configure backend CORS settings

## 📚 Documentation

### Component Documentation
Each component includes:
- **Props interface** with TypeScript
- **Usage examples** in comments
- **Accessibility** considerations

### API Services
- **Type-safe** API calls
- **Error handling** with try/catch
- **Loading states** management
- **Caching** strategies

## 🤝 Contributing

### Development Workflow
1. Create feature branch
2. Make changes in `src/`
3. Add/update tests
4. Run linting and tests
5. Submit pull request

### Code Standards
- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for formatting
- **Conventional commits** for git history

## 📄 License
MIT License - see LICENSE file for details

---

**FieldReporter Frontend** - Beautiful, responsive, and powerful! ✨