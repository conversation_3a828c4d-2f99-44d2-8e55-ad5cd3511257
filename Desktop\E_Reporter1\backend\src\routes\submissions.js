const express = require('express');
const router = express.Router();
const submissionsController = require('../controllers/submissionsController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { validateFormSubmission, validateUUID, validatePagination } = require('../middleware/validation');

// Get all submissions
router.get('/', 
  authenticateToken, 
  validatePagination,
  submissionsController.getSubmissions
);

// Get submission statistics
router.get('/stats', 
  authenticateToken,
  submissionsController.getSubmissionStats
);

// Get single submission
router.get('/:id', 
  authenticateToken, 
  validateUUID('id'),
  submissionsController.getSubmissionById
);

// Create new submission
router.post('/', 
  authenticateToken, 
  requirePermission('submit_checksheet'),
  validateFormSubmission,
  submissionsController.createSubmission
);

// Update submission status (approve/reject)
router.patch('/:id/status', 
  authenticateToken,
  validateUUID('id'),
  submissionsController.updateSubmissionStatus
);

// Delete submission
router.delete('/:id', 
  authenticateToken,
  validateUUID('id'),
  submissionsController.deleteSubmission
);

module.exports = router;