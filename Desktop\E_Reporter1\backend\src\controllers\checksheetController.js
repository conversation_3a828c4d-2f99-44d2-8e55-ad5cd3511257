const { v4: uuidv4 } = require('uuid');
const database = require('../config/database');

// Get all checksheet sessions for user
const getChecksheetSessions = async (req, res) => {
  try {
    const user = req.user;
    const { page = 1, limit = 10, status, form_id } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE cs.user_id = @userId';
    let params = { userId: user.id };

    // Status filter
    if (status) {
      whereClause += ' AND cs.status = @status';
      params.status = status;
    }

    // Form filter
    if (form_id) {
      whereClause += ' AND cs.form_id = @formId';
      params.formId = form_id;
    }

    const query = `
      SELECT * FROM vw_checksheet_sessions_complete
      ${whereClause}
      ORDER BY started_at DESC
      OFFSET @offset ROWS
      FETCH NEXT @limit ROWS ONLY
    `;

    params.offset = offset;
    params.limit = parseInt(limit);

    const result = await database.query(query, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM checksheet_sessions cs
      ${whereClause}
    `;
    
    const countResult = await database.query(countQuery, params);
    const total = countResult.recordset[0].total;

    res.json({
      success: true,
      data: {
        sessions: result.recordset,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get checksheet sessions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get checksheet sessions'
    });
  }
};

// Get single checksheet session by ID
const getChecksheetSessionById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    const query = `
      SELECT * FROM vw_checksheet_sessions_complete
      WHERE id = @sessionId AND user_id = @userId
    `;

    const result = await database.query(query, {
      sessionId: id,
      userId: user.id
    });

    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Checksheet session not found or access denied'
      });
    }

    const session = result.recordset[0];

    // Get responses for this session
    const responsesResult = await database.query(
      'SELECT * FROM checksheet_responses WHERE session_id = @sessionId ORDER BY created_at',
      { sessionId: id }
    );

    session.responses = responsesResult.recordset;

    res.json({
      success: true,
      data: { session }
    });

  } catch (error) {
    console.error('Get checksheet session by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get checksheet session'
    });
  }
};

// Start new checksheet session
const startChecksheetSession = async (req, res) => {
  try {
    const {
      form_id,
      session_name,
      latitude,
      longitude,
      location_name,
      device_info
    } = req.body;

    const user = req.user;

    // Verify form exists and user has access
    const formResult = await database.query(`
      SELECT id, name, json_schema, excel_structure
      FROM forms 
      WHERE id = @formId 
        AND is_active = 1
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
    `, {
      formId: form_id,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (formResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form not found or access denied'
      });
    }

    const form = formResult.recordset[0];

    // Use stored procedure to start session
    const result = await database.execute('sp_StartChecksheetSession', {
      FormId: form_id,
      UserId: user.id,
      SessionName: session_name,
      Latitude: latitude || null,
      Longitude: longitude || null,
      LocationName: location_name || null,
      DeviceInfo: device_info ? JSON.stringify(device_info) : null
    });

    res.status(201).json({
      success: true,
      message: 'Checksheet session started successfully',
      data: { session: result.recordset[0] }
    });

  } catch (error) {
    console.error('Start checksheet session error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start checksheet session'
    });
  }
};

// Save checksheet response
const saveChecksheetResponse = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      question_id,
      question_text,
      question_type,
      response_value,
      response_data,
      is_required = false
    } = req.body;

    const user = req.user;

    // Verify session belongs to user
    const sessionResult = await database.query(
      'SELECT id FROM checksheet_sessions WHERE id = @sessionId AND user_id = @userId',
      { sessionId: id, userId: user.id }
    );

    if (sessionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Checksheet session not found or access denied'
      });
    }

    // Use stored procedure to save response
    await database.execute('sp_SaveChecksheetResponse', {
      SessionId: id,
      QuestionId: question_id,
      QuestionText: question_text,
      QuestionType: question_type,
      ResponseValue: response_value,
      ResponseData: response_data ? JSON.stringify(response_data) : null,
      IsRequired: is_required
    });

    res.json({
      success: true,
      message: 'Response saved successfully'
    });

  } catch (error) {
    console.error('Save checksheet response error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save response'
    });
  }
};

// Complete checksheet session
const completeChecksheetSession = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Verify session belongs to user
    const sessionResult = await database.query(
      'SELECT id, status FROM checksheet_sessions WHERE id = @sessionId AND user_id = @userId',
      { sessionId: id, userId: user.id }
    );

    if (sessionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Checksheet session not found or access denied'
      });
    }

    const session = sessionResult.recordset[0];

    if (session.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Checksheet session is already completed'
      });
    }

    // Use stored procedure to complete session
    const result = await database.execute('sp_CompleteChecksheetSession', {
      SessionId: id
    });

    res.json({
      success: true,
      message: 'Checksheet session completed successfully',
      data: { session: result.recordset[0] }
    });

  } catch (error) {
    console.error('Complete checksheet session error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete checksheet session'
    });
  }
};

// Get checksheet responses for session
const getChecksheetResponses = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Verify session belongs to user or user has permission to view
    const sessionResult = await database.query(`
      SELECT cs.id, cs.user_id, f.general_department, f.next_department, f.direct_department
      FROM checksheet_sessions cs
      LEFT JOIN forms f ON cs.form_id = f.id
      WHERE cs.id = @sessionId
    `, { sessionId: id });

    if (sessionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Checksheet session not found'
      });
    }

    const session = sessionResult.recordset[0];

    // Check access permissions
    let hasAccess = false;
    if (user.role === 'admin') {
      hasAccess = true;
    } else if (session.user_id === user.id) {
      hasAccess = true;
    } else if (user.role === 'final_approval' && 
               session.general_department === user.general_department &&
               session.next_department === user.next_department) {
      hasAccess = true;
    } else if ((user.role === 'approval' || user.role === 'submit') &&
               session.general_department === user.general_department &&
               session.next_department === user.next_department &&
               session.direct_department === user.direct_department) {
      hasAccess = true;
    }

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Get responses
    const responsesResult = await database.query(
      'SELECT * FROM checksheet_responses WHERE session_id = @sessionId ORDER BY created_at',
      { sessionId: id }
    );

    res.json({
      success: true,
      data: {
        responses: responsesResult.recordset
      }
    });

  } catch (error) {
    console.error('Get checksheet responses error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get checksheet responses'
    });
  }
};

// Upload attachment for checksheet
const uploadChecksheetAttachment = async (req, res) => {
  try {
    const { id } = req.params;
    const { response_id, description } = req.body;
    const user = req.user;

    // TODO: Implement file upload logic
    // This would typically use multer or similar middleware

    res.json({
      success: true,
      message: 'File upload functionality not yet implemented'
    });

  } catch (error) {
    console.error('Upload checksheet attachment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload attachment'
    });
  }
};

// Get form templates
const getFormTemplates = async (req, res) => {
  try {
    const { category, is_public } = req.query;
    const user = req.user;

    let whereClause = 'WHERE 1=1';
    let params = {};

    // Category filter
    if (category) {
      whereClause += ' AND category = @category';
      params.category = category;
    }

    // Public filter or user's own templates
    if (is_public === 'true') {
      whereClause += ' AND is_public = 1';
    } else {
      whereClause += ' AND (is_public = 1 OR created_by = @userId)';
      params.userId = user.id;
    }

    const query = `
      SELECT 
        ft.*,
        u.username as created_by_name
      FROM form_templates ft
      LEFT JOIN users u ON ft.created_by = u.id
      ${whereClause}
      ORDER BY usage_count DESC, created_at DESC
    `;

    const result = await database.query(query, params);

    res.json({
      success: true,
      data: {
        templates: result.recordset
      }
    });

  } catch (error) {
    console.error('Get form templates error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get form templates'
    });
  }
};

module.exports = {
  getChecksheetSessions,
  getChecksheetSessionById,
  startChecksheetSession,
  saveChecksheetResponse,
  completeChecksheetSession,
  getChecksheetResponses,
  uploadChecksheetAttachment,
  getFormTemplates
};