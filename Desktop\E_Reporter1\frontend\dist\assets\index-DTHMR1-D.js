(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const l of s)if(l.type==="childList")for(const o of l.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const l={};return s.integrity&&(l.integrity=s.integrity),s.referrerPolicy&&(l.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?l.credentials="include":s.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(s){if(s.ep)return;s.ep=!0;const l=n(s);fetch(s.href,l)}})();function fm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ac={exports:{}},fl={},oc={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zr=Symbol.for("react.element"),mm=Symbol.for("react.portal"),pm=Symbol.for("react.fragment"),hm=Symbol.for("react.strict_mode"),gm=Symbol.for("react.profiler"),xm=Symbol.for("react.provider"),ym=Symbol.for("react.context"),vm=Symbol.for("react.forward_ref"),wm=Symbol.for("react.suspense"),Nm=Symbol.for("react.memo"),jm=Symbol.for("react.lazy"),Ri=Symbol.iterator;function km(e){return e===null||typeof e!="object"?null:(e=Ri&&e[Ri]||e["@@iterator"],typeof e=="function"?e:null)}var ic={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},uc=Object.assign,cc={};function zn(e,t,n){this.props=e,this.context=t,this.refs=cc,this.updater=n||ic}zn.prototype.isReactComponent={};zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function dc(){}dc.prototype=zn.prototype;function No(e,t,n){this.props=e,this.context=t,this.refs=cc,this.updater=n||ic}var jo=No.prototype=new dc;jo.constructor=No;uc(jo,zn.prototype);jo.isPureReactComponent=!0;var Ti=Array.isArray,fc=Object.prototype.hasOwnProperty,ko={current:null},mc={key:!0,ref:!0,__self:!0,__source:!0};function pc(e,t,n){var r,s={},l=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(l=""+t.key),t)fc.call(t,r)&&!mc.hasOwnProperty(r)&&(s[r]=t[r]);var i=arguments.length-2;if(i===1)s.children=n;else if(1<i){for(var u=Array(i),c=0;c<i;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)s[r]===void 0&&(s[r]=i[r]);return{$$typeof:zr,type:e,key:l,ref:o,props:s,_owner:ko.current}}function Sm(e,t){return{$$typeof:zr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function So(e){return typeof e=="object"&&e!==null&&e.$$typeof===zr}function Em(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Pi=/\/+/g;function $l(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Em(""+e.key):t.toString(36)}function ws(e,t,n,r,s){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(l){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case zr:case mm:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+$l(o,0):r,Ti(s)?(n="",e!=null&&(n=e.replace(Pi,"$&/")+"/"),ws(s,t,n,"",function(c){return c})):s!=null&&(So(s)&&(s=Sm(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Pi,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Ti(e))for(var i=0;i<e.length;i++){l=e[i];var u=r+$l(l,i);o+=ws(l,t,n,u,s)}else if(u=km(e),typeof u=="function")for(e=u.call(e),i=0;!(l=e.next()).done;)l=l.value,u=r+$l(l,i++),o+=ws(l,t,n,u,s);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Zr(e,t,n){if(e==null)return e;var r=[],s=0;return ws(e,r,"","",function(l){return t.call(n,l,s++)}),r}function bm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ye={current:null},Ns={transition:null},Cm={ReactCurrentDispatcher:ye,ReactCurrentBatchConfig:Ns,ReactCurrentOwner:ko};function hc(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:Zr,forEach:function(e,t,n){Zr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Zr(e,function(){t++}),t},toArray:function(e){return Zr(e,function(t){return t})||[]},only:function(e){if(!So(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=zn;U.Fragment=pm;U.Profiler=gm;U.PureComponent=No;U.StrictMode=hm;U.Suspense=wm;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cm;U.act=hc;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=uc({},e.props),s=e.key,l=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,o=ko.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(u in t)fc.call(t,u)&&!mc.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&i!==void 0?i[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){i=Array(u);for(var c=0;c<u;c++)i[c]=arguments[c+2];r.children=i}return{$$typeof:zr,type:e.type,key:s,ref:l,props:r,_owner:o}};U.createContext=function(e){return e={$$typeof:ym,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:xm,_context:e},e.Consumer=e};U.createElement=pc;U.createFactory=function(e){var t=pc.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:vm,render:e}};U.isValidElement=So;U.lazy=function(e){return{$$typeof:jm,_payload:{_status:-1,_result:e},_init:bm}};U.memo=function(e,t){return{$$typeof:Nm,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=Ns.transition;Ns.transition={};try{e()}finally{Ns.transition=t}};U.unstable_act=hc;U.useCallback=function(e,t){return ye.current.useCallback(e,t)};U.useContext=function(e){return ye.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return ye.current.useDeferredValue(e)};U.useEffect=function(e,t){return ye.current.useEffect(e,t)};U.useId=function(){return ye.current.useId()};U.useImperativeHandle=function(e,t,n){return ye.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return ye.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return ye.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return ye.current.useMemo(e,t)};U.useReducer=function(e,t,n){return ye.current.useReducer(e,t,n)};U.useRef=function(e){return ye.current.useRef(e)};U.useState=function(e){return ye.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return ye.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return ye.current.useTransition()};U.version="18.3.1";oc.exports=U;var v=oc.exports;const _m=fm(v);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rm=v,Tm=Symbol.for("react.element"),Pm=Symbol.for("react.fragment"),Lm=Object.prototype.hasOwnProperty,Om=Rm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Fm={key:!0,ref:!0,__self:!0,__source:!0};function gc(e,t,n){var r,s={},l=null,o=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Lm.call(t,r)&&!Fm.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Tm,type:e,key:l,ref:o,props:s,_owner:Om.current}}fl.Fragment=Pm;fl.jsx=gc;fl.jsxs=gc;ac.exports=fl;var a=ac.exports,xc={exports:{}},Ae={},yc={exports:{}},vc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(_,A){var z=_.length;_.push(A);e:for(;0<z;){var J=z-1>>>1,le=_[J];if(0<s(le,A))_[J]=A,_[z]=le,z=J;else break e}}function n(_){return _.length===0?null:_[0]}function r(_){if(_.length===0)return null;var A=_[0],z=_.pop();if(z!==A){_[0]=z;e:for(var J=0,le=_.length,Yr=le>>>1;J<Yr;){var Vt=2*(J+1)-1,Il=_[Vt],Wt=Vt+1,Gr=_[Wt];if(0>s(Il,z))Wt<le&&0>s(Gr,Il)?(_[J]=Gr,_[Wt]=z,J=Wt):(_[J]=Il,_[Vt]=z,J=Vt);else if(Wt<le&&0>s(Gr,z))_[J]=Gr,_[Wt]=z,J=Wt;else break e}}return A}function s(_,A){var z=_.sortIndex-A.sortIndex;return z!==0?z:_.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var o=Date,i=o.now();e.unstable_now=function(){return o.now()-i}}var u=[],c=[],d=1,g=null,p=3,w=!1,x=!1,y=!1,N=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(_){for(var A=n(c);A!==null;){if(A.callback===null)r(c);else if(A.startTime<=_)r(c),A.sortIndex=A.expirationTime,t(u,A);else break;A=n(c)}}function k(_){if(y=!1,f(_),!x)if(n(u)!==null)x=!0,V(E);else{var A=n(c);A!==null&&Ve(k,A.startTime-_)}}function E(_,A){x=!1,y&&(y=!1,h(R),R=-1),w=!0;var z=p;try{for(f(A),g=n(u);g!==null&&(!(g.expirationTime>A)||_&&!P());){var J=g.callback;if(typeof J=="function"){g.callback=null,p=g.priorityLevel;var le=J(g.expirationTime<=A);A=e.unstable_now(),typeof le=="function"?g.callback=le:g===n(u)&&r(u),f(A)}else r(u);g=n(u)}if(g!==null)var Yr=!0;else{var Vt=n(c);Vt!==null&&Ve(k,Vt.startTime-A),Yr=!1}return Yr}finally{g=null,p=z,w=!1}}var C=!1,S=null,R=-1,O=5,F=-1;function P(){return!(e.unstable_now()-F<O)}function L(){if(S!==null){var _=e.unstable_now();F=_;var A=!0;try{A=S(!0,_)}finally{A?B():(C=!1,S=null)}}else C=!1}var B;if(typeof m=="function")B=function(){m(L)};else if(typeof MessageChannel<"u"){var we=new MessageChannel,Ht=we.port2;we.port1.onmessage=L,B=function(){Ht.postMessage(null)}}else B=function(){N(L,0)};function V(_){S=_,C||(C=!0,B())}function Ve(_,A){R=N(function(){_(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){x||w||(x=!0,V(E))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(_){switch(p){case 1:case 2:case 3:var A=3;break;default:A=p}var z=p;p=A;try{return _()}finally{p=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,A){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var z=p;p=_;try{return A()}finally{p=z}},e.unstable_scheduleCallback=function(_,A,z){var J=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?J+z:J):z=J,_){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=z+le,_={id:d++,callback:A,priorityLevel:_,startTime:z,expirationTime:le,sortIndex:-1},z>J?(_.sortIndex=z,t(c,_),n(u)===null&&_===n(c)&&(y?(h(R),R=-1):y=!0,Ve(k,z-J))):(_.sortIndex=le,t(u,_),x||w||(x=!0,V(E))),_},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(_){var A=p;return function(){var z=p;p=A;try{return _.apply(this,arguments)}finally{p=z}}}})(vc);yc.exports=vc;var Am=yc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dm=v,Fe=Am;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wc=new Set,gr={};function on(e,t){Rn(e,t),Rn(e+"Capture",t)}function Rn(e,t){for(gr[e]=t,e=0;e<t.length;e++)wc.add(t[e])}var gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ya=Object.prototype.hasOwnProperty,Mm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Li={},Oi={};function zm(e){return ya.call(Oi,e)?!0:ya.call(Li,e)?!1:Mm.test(e)?Oi[e]=!0:(Li[e]=!0,!1)}function Um(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Im(e,t,n,r){if(t===null||typeof t>"u"||Um(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ve(e,t,n,r,s,l,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new ve(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new ve(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new ve(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new ve(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new ve(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new ve(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new ve(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new ve(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new ve(e,5,!1,e.toLowerCase(),null,!1,!1)});var Eo=/[\-:]([a-z])/g;function bo(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Eo,bo);ce[t]=new ve(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Eo,bo);ce[t]=new ve(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Eo,bo);ce[t]=new ve(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new ve(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new ve("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new ve(e,1,!1,e.toLowerCase(),null,!0,!0)});function Co(e,t,n,r){var s=ce.hasOwnProperty(t)?ce[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Im(t,n,s,r)&&(n=null),r||s===null?zm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Nt=Dm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,es=Symbol.for("react.element"),dn=Symbol.for("react.portal"),fn=Symbol.for("react.fragment"),_o=Symbol.for("react.strict_mode"),va=Symbol.for("react.profiler"),Nc=Symbol.for("react.provider"),jc=Symbol.for("react.context"),Ro=Symbol.for("react.forward_ref"),wa=Symbol.for("react.suspense"),Na=Symbol.for("react.suspense_list"),To=Symbol.for("react.memo"),kt=Symbol.for("react.lazy"),kc=Symbol.for("react.offscreen"),Fi=Symbol.iterator;function Qn(e){return e===null||typeof e!="object"?null:(e=Fi&&e[Fi]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,Bl;function nr(e){if(Bl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bl=t&&t[1]||""}return`
`+Bl+e}var Hl=!1;function Vl(e,t){if(!e||Hl)return"";Hl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),l=r.stack.split(`
`),o=s.length-1,i=l.length-1;1<=o&&0<=i&&s[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(s[o]!==l[i]){if(o!==1||i!==1)do if(o--,i--,0>i||s[o]!==l[i]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=i);break}}}finally{Hl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?nr(e):""}function $m(e){switch(e.tag){case 5:return nr(e.type);case 16:return nr("Lazy");case 13:return nr("Suspense");case 19:return nr("SuspenseList");case 0:case 2:case 15:return e=Vl(e.type,!1),e;case 11:return e=Vl(e.type.render,!1),e;case 1:return e=Vl(e.type,!0),e;default:return""}}function ja(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case fn:return"Fragment";case dn:return"Portal";case va:return"Profiler";case _o:return"StrictMode";case wa:return"Suspense";case Na:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case jc:return(e.displayName||"Context")+".Consumer";case Nc:return(e._context.displayName||"Context")+".Provider";case Ro:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case To:return t=e.displayName||null,t!==null?t:ja(e.type)||"Memo";case kt:t=e._payload,e=e._init;try{return ja(e(t))}catch{}}return null}function Bm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ja(t);case 8:return t===_o?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Mt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Sc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Hm(e){var t=Sc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,l.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ts(e){e._valueTracker||(e._valueTracker=Hm(e))}function Ec(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Sc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function zs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ka(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ai(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Mt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function bc(e,t){t=t.checked,t!=null&&Co(e,"checked",t,!1)}function Sa(e,t){bc(e,t);var n=Mt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ea(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ea(e,t.type,Mt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Di(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ea(e,t,n){(t!=="number"||zs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var rr=Array.isArray;function kn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Mt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function ba(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Mi(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(rr(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Mt(n)}}function Cc(e,t){var n=Mt(t.value),r=Mt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function zi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function _c(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ca(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?_c(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ns,Rc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ns=ns||document.createElement("div"),ns.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ns.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function xr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ar={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Vm=["Webkit","ms","Moz","O"];Object.keys(ar).forEach(function(e){Vm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ar[t]=ar[e]})});function Tc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ar.hasOwnProperty(e)&&ar[e]?(""+t).trim():t+"px"}function Pc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Tc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Wm=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function _a(e,t){if(t){if(Wm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function Ra(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ta=null;function Po(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Pa=null,Sn=null,En=null;function Ui(e){if(e=$r(e)){if(typeof Pa!="function")throw Error(b(280));var t=e.stateNode;t&&(t=xl(t),Pa(e.stateNode,e.type,t))}}function Lc(e){Sn?En?En.push(e):En=[e]:Sn=e}function Oc(){if(Sn){var e=Sn,t=En;if(En=Sn=null,Ui(e),t)for(e=0;e<t.length;e++)Ui(t[e])}}function Fc(e,t){return e(t)}function Ac(){}var Wl=!1;function Dc(e,t,n){if(Wl)return e(t,n);Wl=!0;try{return Fc(e,t,n)}finally{Wl=!1,(Sn!==null||En!==null)&&(Ac(),Oc())}}function yr(e,t){var n=e.stateNode;if(n===null)return null;var r=xl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var La=!1;if(gt)try{var qn={};Object.defineProperty(qn,"passive",{get:function(){La=!0}}),window.addEventListener("test",qn,qn),window.removeEventListener("test",qn,qn)}catch{La=!1}function Qm(e,t,n,r,s,l,o,i,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var or=!1,Us=null,Is=!1,Oa=null,qm={onError:function(e){or=!0,Us=e}};function Km(e,t,n,r,s,l,o,i,u){or=!1,Us=null,Qm.apply(qm,arguments)}function Jm(e,t,n,r,s,l,o,i,u){if(Km.apply(this,arguments),or){if(or){var c=Us;or=!1,Us=null}else throw Error(b(198));Is||(Is=!0,Oa=c)}}function un(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Mc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ii(e){if(un(e)!==e)throw Error(b(188))}function Xm(e){var t=e.alternate;if(!t){if(t=un(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var l=s.alternate;if(l===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===l.child){for(l=s.child;l;){if(l===n)return Ii(s),e;if(l===r)return Ii(s),t;l=l.sibling}throw Error(b(188))}if(n.return!==r.return)n=s,r=l;else{for(var o=!1,i=s.child;i;){if(i===n){o=!0,n=s,r=l;break}if(i===r){o=!0,r=s,n=l;break}i=i.sibling}if(!o){for(i=l.child;i;){if(i===n){o=!0,n=l,r=s;break}if(i===r){o=!0,r=l,n=s;break}i=i.sibling}if(!o)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function zc(e){return e=Xm(e),e!==null?Uc(e):null}function Uc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Uc(e);if(t!==null)return t;e=e.sibling}return null}var Ic=Fe.unstable_scheduleCallback,$i=Fe.unstable_cancelCallback,Ym=Fe.unstable_shouldYield,Gm=Fe.unstable_requestPaint,te=Fe.unstable_now,Zm=Fe.unstable_getCurrentPriorityLevel,Lo=Fe.unstable_ImmediatePriority,$c=Fe.unstable_UserBlockingPriority,$s=Fe.unstable_NormalPriority,ep=Fe.unstable_LowPriority,Bc=Fe.unstable_IdlePriority,ml=null,it=null;function tp(e){if(it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(ml,e,void 0,(e.current.flags&128)===128)}catch{}}var Je=Math.clz32?Math.clz32:sp,np=Math.log,rp=Math.LN2;function sp(e){return e>>>=0,e===0?32:31-(np(e)/rp|0)|0}var rs=64,ss=4194304;function sr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Bs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,l=e.pingedLanes,o=n&268435455;if(o!==0){var i=o&~s;i!==0?r=sr(i):(l&=o,l!==0&&(r=sr(l)))}else o=n&~s,o!==0?r=sr(o):l!==0&&(r=sr(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,l=t&-t,s>=l||s===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Je(t),s=1<<n,r|=e[n],t&=~s;return r}function lp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ap(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-Je(l),i=1<<o,u=s[o];u===-1?(!(i&n)||i&r)&&(s[o]=lp(i,t)):u<=t&&(e.expiredLanes|=i),l&=~i}}function Fa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hc(){var e=rs;return rs<<=1,!(rs&4194240)&&(rs=64),e}function Ql(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ur(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Je(t),e[t]=n}function op(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Je(n),l=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~l}}function Oo(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Je(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var $=0;function Vc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Wc,Fo,Qc,qc,Kc,Aa=!1,ls=[],Rt=null,Tt=null,Pt=null,vr=new Map,wr=new Map,Et=[],ip="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Bi(e,t){switch(e){case"focusin":case"focusout":Rt=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":vr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":wr.delete(t.pointerId)}}function Kn(e,t,n,r,s,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[s]},t!==null&&(t=$r(t),t!==null&&Fo(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function up(e,t,n,r,s){switch(t){case"focusin":return Rt=Kn(Rt,e,t,n,r,s),!0;case"dragenter":return Tt=Kn(Tt,e,t,n,r,s),!0;case"mouseover":return Pt=Kn(Pt,e,t,n,r,s),!0;case"pointerover":var l=s.pointerId;return vr.set(l,Kn(vr.get(l)||null,e,t,n,r,s)),!0;case"gotpointercapture":return l=s.pointerId,wr.set(l,Kn(wr.get(l)||null,e,t,n,r,s)),!0}return!1}function Jc(e){var t=Kt(e.target);if(t!==null){var n=un(t);if(n!==null){if(t=n.tag,t===13){if(t=Mc(n),t!==null){e.blockedOn=t,Kc(e.priority,function(){Qc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function js(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Da(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ta=r,n.target.dispatchEvent(r),Ta=null}else return t=$r(n),t!==null&&Fo(t),e.blockedOn=n,!1;t.shift()}return!0}function Hi(e,t,n){js(e)&&n.delete(t)}function cp(){Aa=!1,Rt!==null&&js(Rt)&&(Rt=null),Tt!==null&&js(Tt)&&(Tt=null),Pt!==null&&js(Pt)&&(Pt=null),vr.forEach(Hi),wr.forEach(Hi)}function Jn(e,t){e.blockedOn===t&&(e.blockedOn=null,Aa||(Aa=!0,Fe.unstable_scheduleCallback(Fe.unstable_NormalPriority,cp)))}function Nr(e){function t(s){return Jn(s,e)}if(0<ls.length){Jn(ls[0],e);for(var n=1;n<ls.length;n++){var r=ls[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Rt!==null&&Jn(Rt,e),Tt!==null&&Jn(Tt,e),Pt!==null&&Jn(Pt,e),vr.forEach(t),wr.forEach(t),n=0;n<Et.length;n++)r=Et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Et.length&&(n=Et[0],n.blockedOn===null);)Jc(n),n.blockedOn===null&&Et.shift()}var bn=Nt.ReactCurrentBatchConfig,Hs=!0;function dp(e,t,n,r){var s=$,l=bn.transition;bn.transition=null;try{$=1,Ao(e,t,n,r)}finally{$=s,bn.transition=l}}function fp(e,t,n,r){var s=$,l=bn.transition;bn.transition=null;try{$=4,Ao(e,t,n,r)}finally{$=s,bn.transition=l}}function Ao(e,t,n,r){if(Hs){var s=Da(e,t,n,r);if(s===null)na(e,t,r,Vs,n),Bi(e,r);else if(up(s,e,t,n,r))r.stopPropagation();else if(Bi(e,r),t&4&&-1<ip.indexOf(e)){for(;s!==null;){var l=$r(s);if(l!==null&&Wc(l),l=Da(e,t,n,r),l===null&&na(e,t,r,Vs,n),l===s)break;s=l}s!==null&&r.stopPropagation()}else na(e,t,r,null,n)}}var Vs=null;function Da(e,t,n,r){if(Vs=null,e=Po(r),e=Kt(e),e!==null)if(t=un(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Mc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Vs=e,null}function Xc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zm()){case Lo:return 1;case $c:return 4;case $s:case ep:return 16;case Bc:return 536870912;default:return 16}default:return 16}}var Ct=null,Do=null,ks=null;function Yc(){if(ks)return ks;var e,t=Do,n=t.length,r,s="value"in Ct?Ct.value:Ct.textContent,l=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[l-r];r++);return ks=s.slice(e,1<r?1-r:void 0)}function Ss(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function as(){return!0}function Vi(){return!1}function De(e){function t(n,r,s,l,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=l,this.target=o,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(l):l[i]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?as:Vi,this.isPropagationStopped=Vi,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=as)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=as)},persist:function(){},isPersistent:as}),t}var Un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Mo=De(Un),Ir=G({},Un,{view:0,detail:0}),mp=De(Ir),ql,Kl,Xn,pl=G({},Ir,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:zo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xn&&(Xn&&e.type==="mousemove"?(ql=e.screenX-Xn.screenX,Kl=e.screenY-Xn.screenY):Kl=ql=0,Xn=e),ql)},movementY:function(e){return"movementY"in e?e.movementY:Kl}}),Wi=De(pl),pp=G({},pl,{dataTransfer:0}),hp=De(pp),gp=G({},Ir,{relatedTarget:0}),Jl=De(gp),xp=G({},Un,{animationName:0,elapsedTime:0,pseudoElement:0}),yp=De(xp),vp=G({},Un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),wp=De(vp),Np=G({},Un,{data:0}),Qi=De(Np),jp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ep(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Sp[e])?!!t[e]:!1}function zo(){return Ep}var bp=G({},Ir,{key:function(e){if(e.key){var t=jp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ss(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?kp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:zo,charCode:function(e){return e.type==="keypress"?Ss(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ss(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Cp=De(bp),_p=G({},pl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qi=De(_p),Rp=G({},Ir,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:zo}),Tp=De(Rp),Pp=G({},Un,{propertyName:0,elapsedTime:0,pseudoElement:0}),Lp=De(Pp),Op=G({},pl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Fp=De(Op),Ap=[9,13,27,32],Uo=gt&&"CompositionEvent"in window,ir=null;gt&&"documentMode"in document&&(ir=document.documentMode);var Dp=gt&&"TextEvent"in window&&!ir,Gc=gt&&(!Uo||ir&&8<ir&&11>=ir),Ki=" ",Ji=!1;function Zc(e,t){switch(e){case"keyup":return Ap.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ed(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var mn=!1;function Mp(e,t){switch(e){case"compositionend":return ed(t);case"keypress":return t.which!==32?null:(Ji=!0,Ki);case"textInput":return e=t.data,e===Ki&&Ji?null:e;default:return null}}function zp(e,t){if(mn)return e==="compositionend"||!Uo&&Zc(e,t)?(e=Yc(),ks=Do=Ct=null,mn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Gc&&t.locale!=="ko"?null:t.data;default:return null}}var Up={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Xi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Up[e.type]:t==="textarea"}function td(e,t,n,r){Lc(r),t=Ws(t,"onChange"),0<t.length&&(n=new Mo("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ur=null,jr=null;function Ip(e){fd(e,0)}function hl(e){var t=gn(e);if(Ec(t))return e}function $p(e,t){if(e==="change")return t}var nd=!1;if(gt){var Xl;if(gt){var Yl="oninput"in document;if(!Yl){var Yi=document.createElement("div");Yi.setAttribute("oninput","return;"),Yl=typeof Yi.oninput=="function"}Xl=Yl}else Xl=!1;nd=Xl&&(!document.documentMode||9<document.documentMode)}function Gi(){ur&&(ur.detachEvent("onpropertychange",rd),jr=ur=null)}function rd(e){if(e.propertyName==="value"&&hl(jr)){var t=[];td(t,jr,e,Po(e)),Dc(Ip,t)}}function Bp(e,t,n){e==="focusin"?(Gi(),ur=t,jr=n,ur.attachEvent("onpropertychange",rd)):e==="focusout"&&Gi()}function Hp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return hl(jr)}function Vp(e,t){if(e==="click")return hl(t)}function Wp(e,t){if(e==="input"||e==="change")return hl(t)}function Qp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ye=typeof Object.is=="function"?Object.is:Qp;function kr(e,t){if(Ye(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!ya.call(t,s)||!Ye(e[s],t[s]))return!1}return!0}function Zi(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function eu(e,t){var n=Zi(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Zi(n)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ld(){for(var e=window,t=zs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zs(e.document)}return t}function Io(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function qp(e){var t=ld(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sd(n.ownerDocument.documentElement,n)){if(r!==null&&Io(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,l=Math.min(r.start,s);r=r.end===void 0?l:Math.min(r.end,s),!e.extend&&l>r&&(s=r,r=l,l=s),s=eu(n,l);var o=eu(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Kp=gt&&"documentMode"in document&&11>=document.documentMode,pn=null,Ma=null,cr=null,za=!1;function tu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;za||pn==null||pn!==zs(r)||(r=pn,"selectionStart"in r&&Io(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),cr&&kr(cr,r)||(cr=r,r=Ws(Ma,"onSelect"),0<r.length&&(t=new Mo("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=pn)))}function os(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var hn={animationend:os("Animation","AnimationEnd"),animationiteration:os("Animation","AnimationIteration"),animationstart:os("Animation","AnimationStart"),transitionend:os("Transition","TransitionEnd")},Gl={},ad={};gt&&(ad=document.createElement("div").style,"AnimationEvent"in window||(delete hn.animationend.animation,delete hn.animationiteration.animation,delete hn.animationstart.animation),"TransitionEvent"in window||delete hn.transitionend.transition);function gl(e){if(Gl[e])return Gl[e];if(!hn[e])return e;var t=hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ad)return Gl[e]=t[n];return e}var od=gl("animationend"),id=gl("animationiteration"),ud=gl("animationstart"),cd=gl("transitionend"),dd=new Map,nu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ut(e,t){dd.set(e,t),on(t,[e])}for(var Zl=0;Zl<nu.length;Zl++){var ea=nu[Zl],Jp=ea.toLowerCase(),Xp=ea[0].toUpperCase()+ea.slice(1);Ut(Jp,"on"+Xp)}Ut(od,"onAnimationEnd");Ut(id,"onAnimationIteration");Ut(ud,"onAnimationStart");Ut("dblclick","onDoubleClick");Ut("focusin","onFocus");Ut("focusout","onBlur");Ut(cd,"onTransitionEnd");Rn("onMouseEnter",["mouseout","mouseover"]);Rn("onMouseLeave",["mouseout","mouseover"]);Rn("onPointerEnter",["pointerout","pointerover"]);Rn("onPointerLeave",["pointerout","pointerover"]);on("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));on("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));on("onBeforeInput",["compositionend","keypress","textInput","paste"]);on("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));on("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));on("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var lr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yp=new Set("cancel close invalid load scroll toggle".split(" ").concat(lr));function ru(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Jm(r,t,void 0,e),e.currentTarget=null}function fd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],u=i.instance,c=i.currentTarget;if(i=i.listener,u!==l&&s.isPropagationStopped())break e;ru(s,i,c),l=u}else for(o=0;o<r.length;o++){if(i=r[o],u=i.instance,c=i.currentTarget,i=i.listener,u!==l&&s.isPropagationStopped())break e;ru(s,i,c),l=u}}}if(Is)throw e=Oa,Is=!1,Oa=null,e}function W(e,t){var n=t[Ha];n===void 0&&(n=t[Ha]=new Set);var r=e+"__bubble";n.has(r)||(md(t,e,2,!1),n.add(r))}function ta(e,t,n){var r=0;t&&(r|=4),md(n,e,r,t)}var is="_reactListening"+Math.random().toString(36).slice(2);function Sr(e){if(!e[is]){e[is]=!0,wc.forEach(function(n){n!=="selectionchange"&&(Yp.has(n)||ta(n,!1,e),ta(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[is]||(t[is]=!0,ta("selectionchange",!1,t))}}function md(e,t,n,r){switch(Xc(t)){case 1:var s=dp;break;case 4:s=fp;break;default:s=Ao}n=s.bind(null,t,n,e),s=void 0,!La||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function na(e,t,n,r,s){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var i=r.stateNode.containerInfo;if(i===s||i.nodeType===8&&i.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;i!==null;){if(o=Kt(i),o===null)return;if(u=o.tag,u===5||u===6){r=l=o;continue e}i=i.parentNode}}r=r.return}Dc(function(){var c=l,d=Po(n),g=[];e:{var p=dd.get(e);if(p!==void 0){var w=Mo,x=e;switch(e){case"keypress":if(Ss(n)===0)break e;case"keydown":case"keyup":w=Cp;break;case"focusin":x="focus",w=Jl;break;case"focusout":x="blur",w=Jl;break;case"beforeblur":case"afterblur":w=Jl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Wi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=hp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Tp;break;case od:case id:case ud:w=yp;break;case cd:w=Lp;break;case"scroll":w=mp;break;case"wheel":w=Fp;break;case"copy":case"cut":case"paste":w=wp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=qi}var y=(t&4)!==0,N=!y&&e==="scroll",h=y?p!==null?p+"Capture":null:p;y=[];for(var m=c,f;m!==null;){f=m;var k=f.stateNode;if(f.tag===5&&k!==null&&(f=k,h!==null&&(k=yr(m,h),k!=null&&y.push(Er(m,k,f)))),N)break;m=m.return}0<y.length&&(p=new w(p,x,null,n,d),g.push({event:p,listeners:y}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",p&&n!==Ta&&(x=n.relatedTarget||n.fromElement)&&(Kt(x)||x[xt]))break e;if((w||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,w?(x=n.relatedTarget||n.toElement,w=c,x=x?Kt(x):null,x!==null&&(N=un(x),x!==N||x.tag!==5&&x.tag!==6)&&(x=null)):(w=null,x=c),w!==x)){if(y=Wi,k="onMouseLeave",h="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(y=qi,k="onPointerLeave",h="onPointerEnter",m="pointer"),N=w==null?p:gn(w),f=x==null?p:gn(x),p=new y(k,m+"leave",w,n,d),p.target=N,p.relatedTarget=f,k=null,Kt(d)===c&&(y=new y(h,m+"enter",x,n,d),y.target=f,y.relatedTarget=N,k=y),N=k,w&&x)t:{for(y=w,h=x,m=0,f=y;f;f=cn(f))m++;for(f=0,k=h;k;k=cn(k))f++;for(;0<m-f;)y=cn(y),m--;for(;0<f-m;)h=cn(h),f--;for(;m--;){if(y===h||h!==null&&y===h.alternate)break t;y=cn(y),h=cn(h)}y=null}else y=null;w!==null&&su(g,p,w,y,!1),x!==null&&N!==null&&su(g,N,x,y,!0)}}e:{if(p=c?gn(c):window,w=p.nodeName&&p.nodeName.toLowerCase(),w==="select"||w==="input"&&p.type==="file")var E=$p;else if(Xi(p))if(nd)E=Wp;else{E=Hp;var C=Bp}else(w=p.nodeName)&&w.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(E=Vp);if(E&&(E=E(e,c))){td(g,E,n,d);break e}C&&C(e,p,c),e==="focusout"&&(C=p._wrapperState)&&C.controlled&&p.type==="number"&&Ea(p,"number",p.value)}switch(C=c?gn(c):window,e){case"focusin":(Xi(C)||C.contentEditable==="true")&&(pn=C,Ma=c,cr=null);break;case"focusout":cr=Ma=pn=null;break;case"mousedown":za=!0;break;case"contextmenu":case"mouseup":case"dragend":za=!1,tu(g,n,d);break;case"selectionchange":if(Kp)break;case"keydown":case"keyup":tu(g,n,d)}var S;if(Uo)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else mn?Zc(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(Gc&&n.locale!=="ko"&&(mn||R!=="onCompositionStart"?R==="onCompositionEnd"&&mn&&(S=Yc()):(Ct=d,Do="value"in Ct?Ct.value:Ct.textContent,mn=!0)),C=Ws(c,R),0<C.length&&(R=new Qi(R,e,null,n,d),g.push({event:R,listeners:C}),S?R.data=S:(S=ed(n),S!==null&&(R.data=S)))),(S=Dp?Mp(e,n):zp(e,n))&&(c=Ws(c,"onBeforeInput"),0<c.length&&(d=new Qi("onBeforeInput","beforeinput",null,n,d),g.push({event:d,listeners:c}),d.data=S))}fd(g,t)})}function Er(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ws(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,l=s.stateNode;s.tag===5&&l!==null&&(s=l,l=yr(e,n),l!=null&&r.unshift(Er(e,l,s)),l=yr(e,t),l!=null&&r.push(Er(e,l,s))),e=e.return}return r}function cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function su(e,t,n,r,s){for(var l=t._reactName,o=[];n!==null&&n!==r;){var i=n,u=i.alternate,c=i.stateNode;if(u!==null&&u===r)break;i.tag===5&&c!==null&&(i=c,s?(u=yr(n,l),u!=null&&o.unshift(Er(n,u,i))):s||(u=yr(n,l),u!=null&&o.push(Er(n,u,i)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Gp=/\r\n?/g,Zp=/\u0000|\uFFFD/g;function lu(e){return(typeof e=="string"?e:""+e).replace(Gp,`
`).replace(Zp,"")}function us(e,t,n){if(t=lu(t),lu(e)!==t&&n)throw Error(b(425))}function Qs(){}var Ua=null,Ia=null;function $a(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ba=typeof setTimeout=="function"?setTimeout:void 0,eh=typeof clearTimeout=="function"?clearTimeout:void 0,au=typeof Promise=="function"?Promise:void 0,th=typeof queueMicrotask=="function"?queueMicrotask:typeof au<"u"?function(e){return au.resolve(null).then(e).catch(nh)}:Ba;function nh(e){setTimeout(function(){throw e})}function ra(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),Nr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);Nr(t)}function Lt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ou(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var In=Math.random().toString(36).slice(2),at="__reactFiber$"+In,br="__reactProps$"+In,xt="__reactContainer$"+In,Ha="__reactEvents$"+In,rh="__reactListeners$"+In,sh="__reactHandles$"+In;function Kt(e){var t=e[at];if(t)return t;for(var n=e.parentNode;n;){if(t=n[xt]||n[at]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ou(e);e!==null;){if(n=e[at])return n;e=ou(e)}return t}e=n,n=e.parentNode}return null}function $r(e){return e=e[at]||e[xt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function xl(e){return e[br]||null}var Va=[],xn=-1;function It(e){return{current:e}}function Q(e){0>xn||(e.current=Va[xn],Va[xn]=null,xn--)}function H(e,t){xn++,Va[xn]=e.current,e.current=t}var zt={},he=It(zt),Se=It(!1),tn=zt;function Tn(e,t){var n=e.type.contextTypes;if(!n)return zt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},l;for(l in n)s[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ee(e){return e=e.childContextTypes,e!=null}function qs(){Q(Se),Q(he)}function iu(e,t,n){if(he.current!==zt)throw Error(b(168));H(he,t),H(Se,n)}function pd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(b(108,Bm(e)||"Unknown",s));return G({},n,r)}function Ks(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||zt,tn=he.current,H(he,e),H(Se,Se.current),!0}function uu(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=pd(e,t,tn),r.__reactInternalMemoizedMergedChildContext=e,Q(Se),Q(he),H(he,e)):Q(Se),H(Se,n)}var dt=null,yl=!1,sa=!1;function hd(e){dt===null?dt=[e]:dt.push(e)}function lh(e){yl=!0,hd(e)}function $t(){if(!sa&&dt!==null){sa=!0;var e=0,t=$;try{var n=dt;for($=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}dt=null,yl=!1}catch(s){throw dt!==null&&(dt=dt.slice(e+1)),Ic(Lo,$t),s}finally{$=t,sa=!1}}return null}var yn=[],vn=0,Js=null,Xs=0,ze=[],Ue=0,nn=null,ft=1,mt="";function Qt(e,t){yn[vn++]=Xs,yn[vn++]=Js,Js=e,Xs=t}function gd(e,t,n){ze[Ue++]=ft,ze[Ue++]=mt,ze[Ue++]=nn,nn=e;var r=ft;e=mt;var s=32-Je(r)-1;r&=~(1<<s),n+=1;var l=32-Je(t)+s;if(30<l){var o=s-s%5;l=(r&(1<<o)-1).toString(32),r>>=o,s-=o,ft=1<<32-Je(t)+s|n<<s|r,mt=l+e}else ft=1<<l|n<<s|r,mt=e}function $o(e){e.return!==null&&(Qt(e,1),gd(e,1,0))}function Bo(e){for(;e===Js;)Js=yn[--vn],yn[vn]=null,Xs=yn[--vn],yn[vn]=null;for(;e===nn;)nn=ze[--Ue],ze[Ue]=null,mt=ze[--Ue],ze[Ue]=null,ft=ze[--Ue],ze[Ue]=null}var Oe=null,Pe=null,q=!1,Ke=null;function xd(e,t){var n=Ie(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function cu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Oe=e,Pe=Lt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Oe=e,Pe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=nn!==null?{id:ft,overflow:mt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ie(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Oe=e,Pe=null,!0):!1;default:return!1}}function Wa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Qa(e){if(q){var t=Pe;if(t){var n=t;if(!cu(e,t)){if(Wa(e))throw Error(b(418));t=Lt(n.nextSibling);var r=Oe;t&&cu(e,t)?xd(r,n):(e.flags=e.flags&-4097|2,q=!1,Oe=e)}}else{if(Wa(e))throw Error(b(418));e.flags=e.flags&-4097|2,q=!1,Oe=e}}}function du(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Oe=e}function cs(e){if(e!==Oe)return!1;if(!q)return du(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!$a(e.type,e.memoizedProps)),t&&(t=Pe)){if(Wa(e))throw yd(),Error(b(418));for(;t;)xd(e,t),t=Lt(t.nextSibling)}if(du(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Pe=Lt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Pe=null}}else Pe=Oe?Lt(e.stateNode.nextSibling):null;return!0}function yd(){for(var e=Pe;e;)e=Lt(e.nextSibling)}function Pn(){Pe=Oe=null,q=!1}function Ho(e){Ke===null?Ke=[e]:Ke.push(e)}var ah=Nt.ReactCurrentBatchConfig;function Yn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var s=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(o){var i=s.refs;o===null?delete i[l]:i[l]=o},t._stringRef=l,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function ds(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function fu(e){var t=e._init;return t(e._payload)}function vd(e){function t(h,m){if(e){var f=h.deletions;f===null?(h.deletions=[m],h.flags|=16):f.push(m)}}function n(h,m){if(!e)return null;for(;m!==null;)t(h,m),m=m.sibling;return null}function r(h,m){for(h=new Map;m!==null;)m.key!==null?h.set(m.key,m):h.set(m.index,m),m=m.sibling;return h}function s(h,m){return h=Dt(h,m),h.index=0,h.sibling=null,h}function l(h,m,f){return h.index=f,e?(f=h.alternate,f!==null?(f=f.index,f<m?(h.flags|=2,m):f):(h.flags|=2,m)):(h.flags|=1048576,m)}function o(h){return e&&h.alternate===null&&(h.flags|=2),h}function i(h,m,f,k){return m===null||m.tag!==6?(m=da(f,h.mode,k),m.return=h,m):(m=s(m,f),m.return=h,m)}function u(h,m,f,k){var E=f.type;return E===fn?d(h,m,f.props.children,k,f.key):m!==null&&(m.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===kt&&fu(E)===m.type)?(k=s(m,f.props),k.ref=Yn(h,m,f),k.return=h,k):(k=Ps(f.type,f.key,f.props,null,h.mode,k),k.ref=Yn(h,m,f),k.return=h,k)}function c(h,m,f,k){return m===null||m.tag!==4||m.stateNode.containerInfo!==f.containerInfo||m.stateNode.implementation!==f.implementation?(m=fa(f,h.mode,k),m.return=h,m):(m=s(m,f.children||[]),m.return=h,m)}function d(h,m,f,k,E){return m===null||m.tag!==7?(m=Zt(f,h.mode,k,E),m.return=h,m):(m=s(m,f),m.return=h,m)}function g(h,m,f){if(typeof m=="string"&&m!==""||typeof m=="number")return m=da(""+m,h.mode,f),m.return=h,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case es:return f=Ps(m.type,m.key,m.props,null,h.mode,f),f.ref=Yn(h,null,m),f.return=h,f;case dn:return m=fa(m,h.mode,f),m.return=h,m;case kt:var k=m._init;return g(h,k(m._payload),f)}if(rr(m)||Qn(m))return m=Zt(m,h.mode,f,null),m.return=h,m;ds(h,m)}return null}function p(h,m,f,k){var E=m!==null?m.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return E!==null?null:i(h,m,""+f,k);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case es:return f.key===E?u(h,m,f,k):null;case dn:return f.key===E?c(h,m,f,k):null;case kt:return E=f._init,p(h,m,E(f._payload),k)}if(rr(f)||Qn(f))return E!==null?null:d(h,m,f,k,null);ds(h,f)}return null}function w(h,m,f,k,E){if(typeof k=="string"&&k!==""||typeof k=="number")return h=h.get(f)||null,i(m,h,""+k,E);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case es:return h=h.get(k.key===null?f:k.key)||null,u(m,h,k,E);case dn:return h=h.get(k.key===null?f:k.key)||null,c(m,h,k,E);case kt:var C=k._init;return w(h,m,f,C(k._payload),E)}if(rr(k)||Qn(k))return h=h.get(f)||null,d(m,h,k,E,null);ds(m,k)}return null}function x(h,m,f,k){for(var E=null,C=null,S=m,R=m=0,O=null;S!==null&&R<f.length;R++){S.index>R?(O=S,S=null):O=S.sibling;var F=p(h,S,f[R],k);if(F===null){S===null&&(S=O);break}e&&S&&F.alternate===null&&t(h,S),m=l(F,m,R),C===null?E=F:C.sibling=F,C=F,S=O}if(R===f.length)return n(h,S),q&&Qt(h,R),E;if(S===null){for(;R<f.length;R++)S=g(h,f[R],k),S!==null&&(m=l(S,m,R),C===null?E=S:C.sibling=S,C=S);return q&&Qt(h,R),E}for(S=r(h,S);R<f.length;R++)O=w(S,h,R,f[R],k),O!==null&&(e&&O.alternate!==null&&S.delete(O.key===null?R:O.key),m=l(O,m,R),C===null?E=O:C.sibling=O,C=O);return e&&S.forEach(function(P){return t(h,P)}),q&&Qt(h,R),E}function y(h,m,f,k){var E=Qn(f);if(typeof E!="function")throw Error(b(150));if(f=E.call(f),f==null)throw Error(b(151));for(var C=E=null,S=m,R=m=0,O=null,F=f.next();S!==null&&!F.done;R++,F=f.next()){S.index>R?(O=S,S=null):O=S.sibling;var P=p(h,S,F.value,k);if(P===null){S===null&&(S=O);break}e&&S&&P.alternate===null&&t(h,S),m=l(P,m,R),C===null?E=P:C.sibling=P,C=P,S=O}if(F.done)return n(h,S),q&&Qt(h,R),E;if(S===null){for(;!F.done;R++,F=f.next())F=g(h,F.value,k),F!==null&&(m=l(F,m,R),C===null?E=F:C.sibling=F,C=F);return q&&Qt(h,R),E}for(S=r(h,S);!F.done;R++,F=f.next())F=w(S,h,R,F.value,k),F!==null&&(e&&F.alternate!==null&&S.delete(F.key===null?R:F.key),m=l(F,m,R),C===null?E=F:C.sibling=F,C=F);return e&&S.forEach(function(L){return t(h,L)}),q&&Qt(h,R),E}function N(h,m,f,k){if(typeof f=="object"&&f!==null&&f.type===fn&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case es:e:{for(var E=f.key,C=m;C!==null;){if(C.key===E){if(E=f.type,E===fn){if(C.tag===7){n(h,C.sibling),m=s(C,f.props.children),m.return=h,h=m;break e}}else if(C.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===kt&&fu(E)===C.type){n(h,C.sibling),m=s(C,f.props),m.ref=Yn(h,C,f),m.return=h,h=m;break e}n(h,C);break}else t(h,C);C=C.sibling}f.type===fn?(m=Zt(f.props.children,h.mode,k,f.key),m.return=h,h=m):(k=Ps(f.type,f.key,f.props,null,h.mode,k),k.ref=Yn(h,m,f),k.return=h,h=k)}return o(h);case dn:e:{for(C=f.key;m!==null;){if(m.key===C)if(m.tag===4&&m.stateNode.containerInfo===f.containerInfo&&m.stateNode.implementation===f.implementation){n(h,m.sibling),m=s(m,f.children||[]),m.return=h,h=m;break e}else{n(h,m);break}else t(h,m);m=m.sibling}m=fa(f,h.mode,k),m.return=h,h=m}return o(h);case kt:return C=f._init,N(h,m,C(f._payload),k)}if(rr(f))return x(h,m,f,k);if(Qn(f))return y(h,m,f,k);ds(h,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,m!==null&&m.tag===6?(n(h,m.sibling),m=s(m,f),m.return=h,h=m):(n(h,m),m=da(f,h.mode,k),m.return=h,h=m),o(h)):n(h,m)}return N}var Ln=vd(!0),wd=vd(!1),Ys=It(null),Gs=null,wn=null,Vo=null;function Wo(){Vo=wn=Gs=null}function Qo(e){var t=Ys.current;Q(Ys),e._currentValue=t}function qa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Cn(e,t){Gs=e,Vo=wn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Be(e){var t=e._currentValue;if(Vo!==e)if(e={context:e,memoizedValue:t,next:null},wn===null){if(Gs===null)throw Error(b(308));wn=e,Gs.dependencies={lanes:0,firstContext:e}}else wn=wn.next=e;return t}var Jt=null;function qo(e){Jt===null?Jt=[e]:Jt.push(e)}function Nd(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,qo(t)):(n.next=s.next,s.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var St=!1;function Ko(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function jd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,yt(e,n)}return s=r.interleaved,s===null?(t.next=t,qo(r)):(t.next=s.next,s.next=t),r.interleaved=t,yt(e,n)}function Es(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Oo(e,n)}}function mu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?s=l=o:l=l.next=o,n=n.next}while(n!==null);l===null?s=l=t:l=l.next=t}else s=l=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Zs(e,t,n,r){var s=e.updateQueue;St=!1;var l=s.firstBaseUpdate,o=s.lastBaseUpdate,i=s.shared.pending;if(i!==null){s.shared.pending=null;var u=i,c=u.next;u.next=null,o===null?l=c:o.next=c,o=u;var d=e.alternate;d!==null&&(d=d.updateQueue,i=d.lastBaseUpdate,i!==o&&(i===null?d.firstBaseUpdate=c:i.next=c,d.lastBaseUpdate=u))}if(l!==null){var g=s.baseState;o=0,d=c=u=null,i=l;do{var p=i.lane,w=i.eventTime;if((r&p)===p){d!==null&&(d=d.next={eventTime:w,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var x=e,y=i;switch(p=t,w=n,y.tag){case 1:if(x=y.payload,typeof x=="function"){g=x.call(w,g,p);break e}g=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,p=typeof x=="function"?x.call(w,g,p):x,p==null)break e;g=G({},g,p);break e;case 2:St=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,p=s.effects,p===null?s.effects=[i]:p.push(i))}else w={eventTime:w,lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},d===null?(c=d=w,u=g):d=d.next=w,o|=p;if(i=i.next,i===null){if(i=s.shared.pending,i===null)break;p=i,i=p.next,p.next=null,s.lastBaseUpdate=p,s.shared.pending=null}}while(!0);if(d===null&&(u=g),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else l===null&&(s.shared.lanes=0);sn|=o,e.lanes=o,e.memoizedState=g}}function pu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(b(191,s));s.call(r)}}}var Br={},ut=It(Br),Cr=It(Br),_r=It(Br);function Xt(e){if(e===Br)throw Error(b(174));return e}function Jo(e,t){switch(H(_r,t),H(Cr,e),H(ut,Br),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ca(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ca(t,e)}Q(ut),H(ut,t)}function On(){Q(ut),Q(Cr),Q(_r)}function kd(e){Xt(_r.current);var t=Xt(ut.current),n=Ca(t,e.type);t!==n&&(H(Cr,e),H(ut,n))}function Xo(e){Cr.current===e&&(Q(ut),Q(Cr))}var X=It(0);function el(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var la=[];function Yo(){for(var e=0;e<la.length;e++)la[e]._workInProgressVersionPrimary=null;la.length=0}var bs=Nt.ReactCurrentDispatcher,aa=Nt.ReactCurrentBatchConfig,rn=0,Y=null,re=null,ae=null,tl=!1,dr=!1,Rr=0,oh=0;function de(){throw Error(b(321))}function Go(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ye(e[n],t[n]))return!1;return!0}function Zo(e,t,n,r,s,l){if(rn=l,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,bs.current=e===null||e.memoizedState===null?dh:fh,e=n(r,s),dr){l=0;do{if(dr=!1,Rr=0,25<=l)throw Error(b(301));l+=1,ae=re=null,t.updateQueue=null,bs.current=mh,e=n(r,s)}while(dr)}if(bs.current=nl,t=re!==null&&re.next!==null,rn=0,ae=re=Y=null,tl=!1,t)throw Error(b(300));return e}function ei(){var e=Rr!==0;return Rr=0,e}function lt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?Y.memoizedState=ae=e:ae=ae.next=e,ae}function He(){if(re===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=ae===null?Y.memoizedState:ae.next;if(t!==null)ae=t,re=e;else{if(e===null)throw Error(b(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},ae===null?Y.memoizedState=ae=e:ae=ae.next=e}return ae}function Tr(e,t){return typeof t=="function"?t(e):t}function oa(e){var t=He(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=re,s=r.baseQueue,l=n.pending;if(l!==null){if(s!==null){var o=s.next;s.next=l.next,l.next=o}r.baseQueue=s=l,n.pending=null}if(s!==null){l=s.next,r=r.baseState;var i=o=null,u=null,c=l;do{var d=c.lane;if((rn&d)===d)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var g={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(i=u=g,o=r):u=u.next=g,Y.lanes|=d,sn|=d}c=c.next}while(c!==null&&c!==l);u===null?o=r:u.next=i,Ye(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do l=s.lane,Y.lanes|=l,sn|=l,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ia(e){var t=He(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,l=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do l=e(l,o.action),o=o.next;while(o!==s);Ye(l,t.memoizedState)||(ke=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Sd(){}function Ed(e,t){var n=Y,r=He(),s=t(),l=!Ye(r.memoizedState,s);if(l&&(r.memoizedState=s,ke=!0),r=r.queue,ti(_d.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||ae!==null&&ae.memoizedState.tag&1){if(n.flags|=2048,Pr(9,Cd.bind(null,n,r,s,t),void 0,null),oe===null)throw Error(b(349));rn&30||bd(n,t,s)}return s}function bd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Cd(e,t,n,r){t.value=n,t.getSnapshot=r,Rd(t)&&Td(e)}function _d(e,t,n){return n(function(){Rd(t)&&Td(e)})}function Rd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ye(e,n)}catch{return!0}}function Td(e){var t=yt(e,1);t!==null&&Xe(t,e,1,-1)}function hu(e){var t=lt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Tr,lastRenderedState:e},t.queue=e,e=e.dispatch=ch.bind(null,Y,e),[t.memoizedState,e]}function Pr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Pd(){return He().memoizedState}function Cs(e,t,n,r){var s=lt();Y.flags|=e,s.memoizedState=Pr(1|t,n,void 0,r===void 0?null:r)}function vl(e,t,n,r){var s=He();r=r===void 0?null:r;var l=void 0;if(re!==null){var o=re.memoizedState;if(l=o.destroy,r!==null&&Go(r,o.deps)){s.memoizedState=Pr(t,n,l,r);return}}Y.flags|=e,s.memoizedState=Pr(1|t,n,l,r)}function gu(e,t){return Cs(8390656,8,e,t)}function ti(e,t){return vl(2048,8,e,t)}function Ld(e,t){return vl(4,2,e,t)}function Od(e,t){return vl(4,4,e,t)}function Fd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ad(e,t,n){return n=n!=null?n.concat([e]):null,vl(4,4,Fd.bind(null,t,e),n)}function ni(){}function Dd(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Go(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Md(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Go(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zd(e,t,n){return rn&21?(Ye(n,t)||(n=Hc(),Y.lanes|=n,sn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function ih(e,t){var n=$;$=n!==0&&4>n?n:4,e(!0);var r=aa.transition;aa.transition={};try{e(!1),t()}finally{$=n,aa.transition=r}}function Ud(){return He().memoizedState}function uh(e,t,n){var r=At(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Id(e))$d(t,n);else if(n=Nd(e,t,n,r),n!==null){var s=xe();Xe(n,e,r,s),Bd(n,t,r)}}function ch(e,t,n){var r=At(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Id(e))$d(t,s);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var o=t.lastRenderedState,i=l(o,n);if(s.hasEagerState=!0,s.eagerState=i,Ye(i,o)){var u=t.interleaved;u===null?(s.next=s,qo(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=Nd(e,t,s,r),n!==null&&(s=xe(),Xe(n,e,r,s),Bd(n,t,r))}}function Id(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function $d(e,t){dr=tl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Oo(e,n)}}var nl={readContext:Be,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},dh={readContext:Be,useCallback:function(e,t){return lt().memoizedState=[e,t===void 0?null:t],e},useContext:Be,useEffect:gu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Cs(4194308,4,Fd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Cs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Cs(4,2,e,t)},useMemo:function(e,t){var n=lt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=lt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=uh.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=lt();return e={current:e},t.memoizedState=e},useState:hu,useDebugValue:ni,useDeferredValue:function(e){return lt().memoizedState=e},useTransition:function(){var e=hu(!1),t=e[0];return e=ih.bind(null,e[1]),lt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,s=lt();if(q){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),oe===null)throw Error(b(349));rn&30||bd(r,t,n)}s.memoizedState=n;var l={value:n,getSnapshot:t};return s.queue=l,gu(_d.bind(null,r,l,e),[e]),r.flags|=2048,Pr(9,Cd.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=lt(),t=oe.identifierPrefix;if(q){var n=mt,r=ft;n=(r&~(1<<32-Je(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Rr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=oh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},fh={readContext:Be,useCallback:Dd,useContext:Be,useEffect:ti,useImperativeHandle:Ad,useInsertionEffect:Ld,useLayoutEffect:Od,useMemo:Md,useReducer:oa,useRef:Pd,useState:function(){return oa(Tr)},useDebugValue:ni,useDeferredValue:function(e){var t=He();return zd(t,re.memoizedState,e)},useTransition:function(){var e=oa(Tr)[0],t=He().memoizedState;return[e,t]},useMutableSource:Sd,useSyncExternalStore:Ed,useId:Ud,unstable_isNewReconciler:!1},mh={readContext:Be,useCallback:Dd,useContext:Be,useEffect:ti,useImperativeHandle:Ad,useInsertionEffect:Ld,useLayoutEffect:Od,useMemo:Md,useReducer:ia,useRef:Pd,useState:function(){return ia(Tr)},useDebugValue:ni,useDeferredValue:function(e){var t=He();return re===null?t.memoizedState=e:zd(t,re.memoizedState,e)},useTransition:function(){var e=ia(Tr)[0],t=He().memoizedState;return[e,t]},useMutableSource:Sd,useSyncExternalStore:Ed,useId:Ud,unstable_isNewReconciler:!1};function Qe(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ka(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var wl={isMounted:function(e){return(e=e._reactInternals)?un(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),s=At(e),l=pt(r,s);l.payload=t,n!=null&&(l.callback=n),t=Ot(e,l,s),t!==null&&(Xe(t,e,s,r),Es(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),s=At(e),l=pt(r,s);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=Ot(e,l,s),t!==null&&(Xe(t,e,s,r),Es(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=At(e),s=pt(n,r);s.tag=2,t!=null&&(s.callback=t),t=Ot(e,s,r),t!==null&&(Xe(t,e,r,n),Es(t,e,r))}};function xu(e,t,n,r,s,l,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,o):t.prototype&&t.prototype.isPureReactComponent?!kr(n,r)||!kr(s,l):!0}function Hd(e,t,n){var r=!1,s=zt,l=t.contextType;return typeof l=="object"&&l!==null?l=Be(l):(s=Ee(t)?tn:he.current,r=t.contextTypes,l=(r=r!=null)?Tn(e,s):zt),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=wl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=l),t}function yu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&wl.enqueueReplaceState(t,t.state,null)}function Ja(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Ko(e);var l=t.contextType;typeof l=="object"&&l!==null?s.context=Be(l):(l=Ee(t)?tn:he.current,s.context=Tn(e,l)),s.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(Ka(e,t,l,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&wl.enqueueReplaceState(s,s.state,null),Zs(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Fn(e,t){try{var n="",r=t;do n+=$m(r),r=r.return;while(r);var s=n}catch(l){s=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:s,digest:null}}function ua(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Xa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ph=typeof WeakMap=="function"?WeakMap:Map;function Vd(e,t,n){n=pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){sl||(sl=!0,ao=r),Xa(e,t)},n}function Wd(e,t,n){n=pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Xa(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Xa(e,t),typeof r!="function"&&(Ft===null?Ft=new Set([this]):Ft.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function vu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ph;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=_h.bind(null,e,t,n),t.then(e,e))}function wu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Nu(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=pt(-1,1),t.tag=2,Ot(n,t,1))),n.lanes|=1),e)}var hh=Nt.ReactCurrentOwner,ke=!1;function ge(e,t,n,r){t.child=e===null?wd(t,null,n,r):Ln(t,e.child,n,r)}function ju(e,t,n,r,s){n=n.render;var l=t.ref;return Cn(t,s),r=Zo(e,t,n,r,l,s),n=ei(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,vt(e,t,s)):(q&&n&&$o(t),t.flags|=1,ge(e,t,r,s),t.child)}function ku(e,t,n,r,s){if(e===null){var l=n.type;return typeof l=="function"&&!ci(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,Qd(e,t,l,r,s)):(e=Ps(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&s)){var o=l.memoizedProps;if(n=n.compare,n=n!==null?n:kr,n(o,r)&&e.ref===t.ref)return vt(e,t,s)}return t.flags|=1,e=Dt(l,r),e.ref=t.ref,e.return=t,t.child=e}function Qd(e,t,n,r,s){if(e!==null){var l=e.memoizedProps;if(kr(l,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=l,(e.lanes&s)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,vt(e,t,s)}return Ya(e,t,n,r,s)}function qd(e,t,n){var r=t.pendingProps,s=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},H(jn,Re),Re|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,H(jn,Re),Re|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,H(jn,Re),Re|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,H(jn,Re),Re|=r;return ge(e,t,s,n),t.child}function Kd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ya(e,t,n,r,s){var l=Ee(n)?tn:he.current;return l=Tn(t,l),Cn(t,s),n=Zo(e,t,n,r,l,s),r=ei(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,vt(e,t,s)):(q&&r&&$o(t),t.flags|=1,ge(e,t,n,s),t.child)}function Su(e,t,n,r,s){if(Ee(n)){var l=!0;Ks(t)}else l=!1;if(Cn(t,s),t.stateNode===null)_s(e,t),Hd(t,n,r),Ja(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,i=t.memoizedProps;o.props=i;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=Be(c):(c=Ee(n)?tn:he.current,c=Tn(t,c));var d=n.getDerivedStateFromProps,g=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";g||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==r||u!==c)&&yu(t,o,r,c),St=!1;var p=t.memoizedState;o.state=p,Zs(t,r,o,s),u=t.memoizedState,i!==r||p!==u||Se.current||St?(typeof d=="function"&&(Ka(t,n,d,r),u=t.memoizedState),(i=St||xu(t,n,i,r,p,u,c))?(g||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=i):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,jd(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:Qe(t.type,i),o.props=c,g=t.pendingProps,p=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=Be(u):(u=Ee(n)?tn:he.current,u=Tn(t,u));var w=n.getDerivedStateFromProps;(d=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==g||p!==u)&&yu(t,o,r,u),St=!1,p=t.memoizedState,o.state=p,Zs(t,r,o,s);var x=t.memoizedState;i!==g||p!==x||Se.current||St?(typeof w=="function"&&(Ka(t,n,w,r),x=t.memoizedState),(c=St||xu(t,n,c,r,p,x,u)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,x,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,x,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),o.props=r,o.state=x,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ga(e,t,n,r,l,s)}function Ga(e,t,n,r,s,l){Kd(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&uu(t,n,!1),vt(e,t,l);r=t.stateNode,hh.current=t;var i=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Ln(t,e.child,null,l),t.child=Ln(t,null,i,l)):ge(e,t,i,l),t.memoizedState=r.state,s&&uu(t,n,!0),t.child}function Jd(e){var t=e.stateNode;t.pendingContext?iu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&iu(e,t.context,!1),Jo(e,t.containerInfo)}function Eu(e,t,n,r,s){return Pn(),Ho(s),t.flags|=256,ge(e,t,n,r),t.child}var Za={dehydrated:null,treeContext:null,retryLane:0};function eo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Xd(e,t,n){var r=t.pendingProps,s=X.current,l=!1,o=(t.flags&128)!==0,i;if((i=o)||(i=e!==null&&e.memoizedState===null?!1:(s&2)!==0),i?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),H(X,s&1),e===null)return Qa(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,l?(r=t.mode,l=t.child,o={mode:"hidden",children:o},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=o):l=kl(o,r,0,null),e=Zt(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=eo(n),t.memoizedState=Za,e):ri(t,o));if(s=e.memoizedState,s!==null&&(i=s.dehydrated,i!==null))return gh(e,t,o,r,i,s,n);if(l){l=r.fallback,o=t.mode,s=e.child,i=s.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Dt(s,u),r.subtreeFlags=s.subtreeFlags&14680064),i!==null?l=Dt(i,l):(l=Zt(l,o,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,o=e.child.memoizedState,o=o===null?eo(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},l.memoizedState=o,l.childLanes=e.childLanes&~n,t.memoizedState=Za,r}return l=e.child,e=l.sibling,r=Dt(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ri(e,t){return t=kl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function fs(e,t,n,r){return r!==null&&Ho(r),Ln(t,e.child,null,n),e=ri(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function gh(e,t,n,r,s,l,o){if(n)return t.flags&256?(t.flags&=-257,r=ua(Error(b(422))),fs(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,s=t.mode,r=kl({mode:"visible",children:r.children},s,0,null),l=Zt(l,s,o,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&Ln(t,e.child,null,o),t.child.memoizedState=eo(o),t.memoizedState=Za,l);if(!(t.mode&1))return fs(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var i=r.dgst;return r=i,l=Error(b(419)),r=ua(l,r,void 0),fs(e,t,o,r)}if(i=(o&e.childLanes)!==0,ke||i){if(r=oe,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==l.retryLane&&(l.retryLane=s,yt(e,s),Xe(r,e,s,-1))}return ui(),r=ua(Error(b(421))),fs(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=Rh.bind(null,e),s._reactRetry=t,null):(e=l.treeContext,Pe=Lt(s.nextSibling),Oe=t,q=!0,Ke=null,e!==null&&(ze[Ue++]=ft,ze[Ue++]=mt,ze[Ue++]=nn,ft=e.id,mt=e.overflow,nn=t),t=ri(t,r.children),t.flags|=4096,t)}function bu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),qa(e.return,t,n)}function ca(e,t,n,r,s){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=s)}function Yd(e,t,n){var r=t.pendingProps,s=r.revealOrder,l=r.tail;if(ge(e,t,r.children,n),r=X.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&bu(e,n,t);else if(e.tag===19)bu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(H(X,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&el(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),ca(t,!1,s,n,l);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&el(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}ca(t,!0,n,null,l);break;case"together":ca(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function _s(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function vt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),sn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=Dt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Dt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xh(e,t,n){switch(t.tag){case 3:Jd(t),Pn();break;case 5:kd(t);break;case 1:Ee(t.type)&&Ks(t);break;case 4:Jo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;H(Ys,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(H(X,X.current&1),t.flags|=128,null):n&t.child.childLanes?Xd(e,t,n):(H(X,X.current&1),e=vt(e,t,n),e!==null?e.sibling:null);H(X,X.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Yd(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),H(X,X.current),r)break;return null;case 22:case 23:return t.lanes=0,qd(e,t,n)}return vt(e,t,n)}var Gd,to,Zd,ef;Gd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};to=function(){};Zd=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Xt(ut.current);var l=null;switch(n){case"input":s=ka(e,s),r=ka(e,r),l=[];break;case"select":s=G({},s,{value:void 0}),r=G({},r,{value:void 0}),l=[];break;case"textarea":s=ba(e,s),r=ba(e,r),l=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Qs)}_a(n,r);var o;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var i=s[c];for(o in i)i.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(gr.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var u=r[c];if(i=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==i&&(u!=null||i!=null))if(c==="style")if(i){for(o in i)!i.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&i[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(l||(l=[]),l.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,i=i?i.__html:void 0,u!=null&&i!==u&&(l=l||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(l=l||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(gr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&W("scroll",e),l||i===u||(l=[])):(l=l||[]).push(c,u))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}};ef=function(e,t,n,r){n!==r&&(t.flags|=4)};function Gn(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function yh(e,t,n){var r=t.pendingProps;switch(Bo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(t),null;case 1:return Ee(t.type)&&qs(),fe(t),null;case 3:return r=t.stateNode,On(),Q(Se),Q(he),Yo(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(cs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ke!==null&&(uo(Ke),Ke=null))),to(e,t),fe(t),null;case 5:Xo(t);var s=Xt(_r.current);if(n=t.type,e!==null&&t.stateNode!=null)Zd(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return fe(t),null}if(e=Xt(ut.current),cs(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[at]=t,r[br]=l,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(s=0;s<lr.length;s++)W(lr[s],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":Ai(r,l),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},W("invalid",r);break;case"textarea":Mi(r,l),W("invalid",r)}_a(n,l),s=null;for(var o in l)if(l.hasOwnProperty(o)){var i=l[o];o==="children"?typeof i=="string"?r.textContent!==i&&(l.suppressHydrationWarning!==!0&&us(r.textContent,i,e),s=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(l.suppressHydrationWarning!==!0&&us(r.textContent,i,e),s=["children",""+i]):gr.hasOwnProperty(o)&&i!=null&&o==="onScroll"&&W("scroll",r)}switch(n){case"input":ts(r),Di(r,l,!0);break;case"textarea":ts(r),zi(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=Qs)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=_c(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[at]=t,e[br]=r,Gd(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ra(n,r),n){case"dialog":W("cancel",e),W("close",e),s=r;break;case"iframe":case"object":case"embed":W("load",e),s=r;break;case"video":case"audio":for(s=0;s<lr.length;s++)W(lr[s],e);s=r;break;case"source":W("error",e),s=r;break;case"img":case"image":case"link":W("error",e),W("load",e),s=r;break;case"details":W("toggle",e),s=r;break;case"input":Ai(e,r),s=ka(e,r),W("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=G({},r,{value:void 0}),W("invalid",e);break;case"textarea":Mi(e,r),s=ba(e,r),W("invalid",e);break;default:s=r}_a(n,s),i=s;for(l in i)if(i.hasOwnProperty(l)){var u=i[l];l==="style"?Pc(e,u):l==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Rc(e,u)):l==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&xr(e,u):typeof u=="number"&&xr(e,""+u):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(gr.hasOwnProperty(l)?u!=null&&l==="onScroll"&&W("scroll",e):u!=null&&Co(e,l,u,o))}switch(n){case"input":ts(e),Di(e,r,!1);break;case"textarea":ts(e),zi(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Mt(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?kn(e,!!r.multiple,l,!1):r.defaultValue!=null&&kn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Qs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return fe(t),null;case 6:if(e&&t.stateNode!=null)ef(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=Xt(_r.current),Xt(ut.current),cs(t)){if(r=t.stateNode,n=t.memoizedProps,r[at]=t,(l=r.nodeValue!==n)&&(e=Oe,e!==null))switch(e.tag){case 3:us(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&us(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[at]=t,t.stateNode=r}return fe(t),null;case 13:if(Q(X),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Pe!==null&&t.mode&1&&!(t.flags&128))yd(),Pn(),t.flags|=98560,l=!1;else if(l=cs(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(b(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(b(317));l[at]=t}else Pn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;fe(t),l=!1}else Ke!==null&&(uo(Ke),Ke=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||X.current&1?se===0&&(se=3):ui())),t.updateQueue!==null&&(t.flags|=4),fe(t),null);case 4:return On(),to(e,t),e===null&&Sr(t.stateNode.containerInfo),fe(t),null;case 10:return Qo(t.type._context),fe(t),null;case 17:return Ee(t.type)&&qs(),fe(t),null;case 19:if(Q(X),l=t.memoizedState,l===null)return fe(t),null;if(r=(t.flags&128)!==0,o=l.rendering,o===null)if(r)Gn(l,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=el(e),o!==null){for(t.flags|=128,Gn(l,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,o=l.alternate,o===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=o.childLanes,l.lanes=o.lanes,l.child=o.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=o.memoizedProps,l.memoizedState=o.memoizedState,l.updateQueue=o.updateQueue,l.type=o.type,e=o.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return H(X,X.current&1|2),t.child}e=e.sibling}l.tail!==null&&te()>An&&(t.flags|=128,r=!0,Gn(l,!1),t.lanes=4194304)}else{if(!r)if(e=el(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Gn(l,!0),l.tail===null&&l.tailMode==="hidden"&&!o.alternate&&!q)return fe(t),null}else 2*te()-l.renderingStartTime>An&&n!==1073741824&&(t.flags|=128,r=!0,Gn(l,!1),t.lanes=4194304);l.isBackwards?(o.sibling=t.child,t.child=o):(n=l.last,n!==null?n.sibling=o:t.child=o,l.last=o)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=te(),t.sibling=null,n=X.current,H(X,r?n&1|2:n&1),t):(fe(t),null);case 22:case 23:return ii(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Re&1073741824&&(fe(t),t.subtreeFlags&6&&(t.flags|=8192)):fe(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function vh(e,t){switch(Bo(t),t.tag){case 1:return Ee(t.type)&&qs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return On(),Q(Se),Q(he),Yo(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Xo(t),null;case 13:if(Q(X),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));Pn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(X),null;case 4:return On(),null;case 10:return Qo(t.type._context),null;case 22:case 23:return ii(),null;case 24:return null;default:return null}}var ms=!1,me=!1,wh=typeof WeakSet=="function"?WeakSet:Set,T=null;function Nn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function no(e,t,n){try{n()}catch(r){Z(e,t,r)}}var Cu=!1;function Nh(e,t){if(Ua=Hs,e=ld(),Io(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var o=0,i=-1,u=-1,c=0,d=0,g=e,p=null;t:for(;;){for(var w;g!==n||s!==0&&g.nodeType!==3||(i=o+s),g!==l||r!==0&&g.nodeType!==3||(u=o+r),g.nodeType===3&&(o+=g.nodeValue.length),(w=g.firstChild)!==null;)p=g,g=w;for(;;){if(g===e)break t;if(p===n&&++c===s&&(i=o),p===l&&++d===r&&(u=o),(w=g.nextSibling)!==null)break;g=p,p=g.parentNode}g=w}n=i===-1||u===-1?null:{start:i,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ia={focusedElem:e,selectionRange:n},Hs=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,N=x.memoizedState,h=t.stateNode,m=h.getSnapshotBeforeUpdate(t.elementType===t.type?y:Qe(t.type,y),N);h.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(k){Z(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return x=Cu,Cu=!1,x}function fr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var l=s.destroy;s.destroy=void 0,l!==void 0&&no(t,n,l)}s=s.next}while(s!==r)}}function Nl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ro(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function tf(e){var t=e.alternate;t!==null&&(e.alternate=null,tf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[at],delete t[br],delete t[Ha],delete t[rh],delete t[sh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function nf(e){return e.tag===5||e.tag===3||e.tag===4}function _u(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||nf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function so(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Qs));else if(r!==4&&(e=e.child,e!==null))for(so(e,t,n),e=e.sibling;e!==null;)so(e,t,n),e=e.sibling}function lo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(lo(e,t,n),e=e.sibling;e!==null;)lo(e,t,n),e=e.sibling}var ie=null,qe=!1;function jt(e,t,n){for(n=n.child;n!==null;)rf(e,t,n),n=n.sibling}function rf(e,t,n){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(ml,n)}catch{}switch(n.tag){case 5:me||Nn(n,t);case 6:var r=ie,s=qe;ie=null,jt(e,t,n),ie=r,qe=s,ie!==null&&(qe?(e=ie,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ie.removeChild(n.stateNode));break;case 18:ie!==null&&(qe?(e=ie,n=n.stateNode,e.nodeType===8?ra(e.parentNode,n):e.nodeType===1&&ra(e,n),Nr(e)):ra(ie,n.stateNode));break;case 4:r=ie,s=qe,ie=n.stateNode.containerInfo,qe=!0,jt(e,t,n),ie=r,qe=s;break;case 0:case 11:case 14:case 15:if(!me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var l=s,o=l.destroy;l=l.tag,o!==void 0&&(l&2||l&4)&&no(n,t,o),s=s.next}while(s!==r)}jt(e,t,n);break;case 1:if(!me&&(Nn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Z(n,t,i)}jt(e,t,n);break;case 21:jt(e,t,n);break;case 22:n.mode&1?(me=(r=me)||n.memoizedState!==null,jt(e,t,n),me=r):jt(e,t,n);break;default:jt(e,t,n)}}function Ru(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new wh),t.forEach(function(r){var s=Th.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function We(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var l=e,o=t,i=o;e:for(;i!==null;){switch(i.tag){case 5:ie=i.stateNode,qe=!1;break e;case 3:ie=i.stateNode.containerInfo,qe=!0;break e;case 4:ie=i.stateNode.containerInfo,qe=!0;break e}i=i.return}if(ie===null)throw Error(b(160));rf(l,o,s),ie=null,qe=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){Z(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sf(t,e),t=t.sibling}function sf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(We(t,e),rt(e),r&4){try{fr(3,e,e.return),Nl(3,e)}catch(y){Z(e,e.return,y)}try{fr(5,e,e.return)}catch(y){Z(e,e.return,y)}}break;case 1:We(t,e),rt(e),r&512&&n!==null&&Nn(n,n.return);break;case 5:if(We(t,e),rt(e),r&512&&n!==null&&Nn(n,n.return),e.flags&32){var s=e.stateNode;try{xr(s,"")}catch(y){Z(e,e.return,y)}}if(r&4&&(s=e.stateNode,s!=null)){var l=e.memoizedProps,o=n!==null?n.memoizedProps:l,i=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{i==="input"&&l.type==="radio"&&l.name!=null&&bc(s,l),Ra(i,o);var c=Ra(i,l);for(o=0;o<u.length;o+=2){var d=u[o],g=u[o+1];d==="style"?Pc(s,g):d==="dangerouslySetInnerHTML"?Rc(s,g):d==="children"?xr(s,g):Co(s,d,g,c)}switch(i){case"input":Sa(s,l);break;case"textarea":Cc(s,l);break;case"select":var p=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!l.multiple;var w=l.value;w!=null?kn(s,!!l.multiple,w,!1):p!==!!l.multiple&&(l.defaultValue!=null?kn(s,!!l.multiple,l.defaultValue,!0):kn(s,!!l.multiple,l.multiple?[]:"",!1))}s[br]=l}catch(y){Z(e,e.return,y)}}break;case 6:if(We(t,e),rt(e),r&4){if(e.stateNode===null)throw Error(b(162));s=e.stateNode,l=e.memoizedProps;try{s.nodeValue=l}catch(y){Z(e,e.return,y)}}break;case 3:if(We(t,e),rt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Nr(t.containerInfo)}catch(y){Z(e,e.return,y)}break;case 4:We(t,e),rt(e);break;case 13:We(t,e),rt(e),s=e.child,s.flags&8192&&(l=s.memoizedState!==null,s.stateNode.isHidden=l,!l||s.alternate!==null&&s.alternate.memoizedState!==null||(ai=te())),r&4&&Ru(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(me=(c=me)||d,We(t,e),me=c):We(t,e),rt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(T=e,d=e.child;d!==null;){for(g=T=d;T!==null;){switch(p=T,w=p.child,p.tag){case 0:case 11:case 14:case 15:fr(4,p,p.return);break;case 1:Nn(p,p.return);var x=p.stateNode;if(typeof x.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(y){Z(r,n,y)}}break;case 5:Nn(p,p.return);break;case 22:if(p.memoizedState!==null){Pu(g);continue}}w!==null?(w.return=p,T=w):Pu(g)}d=d.sibling}e:for(d=null,g=e;;){if(g.tag===5){if(d===null){d=g;try{s=g.stateNode,c?(l=s.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(i=g.stateNode,u=g.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,i.style.display=Tc("display",o))}catch(y){Z(e,e.return,y)}}}else if(g.tag===6){if(d===null)try{g.stateNode.nodeValue=c?"":g.memoizedProps}catch(y){Z(e,e.return,y)}}else if((g.tag!==22&&g.tag!==23||g.memoizedState===null||g===e)&&g.child!==null){g.child.return=g,g=g.child;continue}if(g===e)break e;for(;g.sibling===null;){if(g.return===null||g.return===e)break e;d===g&&(d=null),g=g.return}d===g&&(d=null),g.sibling.return=g.return,g=g.sibling}}break;case 19:We(t,e),rt(e),r&4&&Ru(e);break;case 21:break;default:We(t,e),rt(e)}}function rt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(nf(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(xr(s,""),r.flags&=-33);var l=_u(e);lo(e,l,s);break;case 3:case 4:var o=r.stateNode.containerInfo,i=_u(e);so(e,i,o);break;default:throw Error(b(161))}}catch(u){Z(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function jh(e,t,n){T=e,lf(e)}function lf(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var s=T,l=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||ms;if(!o){var i=s.alternate,u=i!==null&&i.memoizedState!==null||me;i=ms;var c=me;if(ms=o,(me=u)&&!c)for(T=s;T!==null;)o=T,u=o.child,o.tag===22&&o.memoizedState!==null?Lu(s):u!==null?(u.return=o,T=u):Lu(s);for(;l!==null;)T=l,lf(l),l=l.sibling;T=s,ms=i,me=c}Tu(e)}else s.subtreeFlags&8772&&l!==null?(l.return=s,T=l):Tu(e)}}function Tu(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:me||Nl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!me)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Qe(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&pu(t,l,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}pu(t,o,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var g=d.dehydrated;g!==null&&Nr(g)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}me||t.flags&512&&ro(t)}catch(p){Z(t,t.return,p)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function Pu(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function Lu(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Nl(4,t)}catch(u){Z(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){Z(t,s,u)}}var l=t.return;try{ro(t)}catch(u){Z(t,l,u)}break;case 5:var o=t.return;try{ro(t)}catch(u){Z(t,o,u)}}}catch(u){Z(t,t.return,u)}if(t===e){T=null;break}var i=t.sibling;if(i!==null){i.return=t.return,T=i;break}T=t.return}}var kh=Math.ceil,rl=Nt.ReactCurrentDispatcher,si=Nt.ReactCurrentOwner,$e=Nt.ReactCurrentBatchConfig,I=0,oe=null,ne=null,ue=0,Re=0,jn=It(0),se=0,Lr=null,sn=0,jl=0,li=0,mr=null,je=null,ai=0,An=1/0,ct=null,sl=!1,ao=null,Ft=null,ps=!1,_t=null,ll=0,pr=0,oo=null,Rs=-1,Ts=0;function xe(){return I&6?te():Rs!==-1?Rs:Rs=te()}function At(e){return e.mode&1?I&2&&ue!==0?ue&-ue:ah.transition!==null?(Ts===0&&(Ts=Hc()),Ts):(e=$,e!==0||(e=window.event,e=e===void 0?16:Xc(e.type)),e):1}function Xe(e,t,n,r){if(50<pr)throw pr=0,oo=null,Error(b(185));Ur(e,n,r),(!(I&2)||e!==oe)&&(e===oe&&(!(I&2)&&(jl|=n),se===4&&bt(e,ue)),be(e,r),n===1&&I===0&&!(t.mode&1)&&(An=te()+500,yl&&$t()))}function be(e,t){var n=e.callbackNode;ap(e,t);var r=Bs(e,e===oe?ue:0);if(r===0)n!==null&&$i(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&$i(n),t===1)e.tag===0?lh(Ou.bind(null,e)):hd(Ou.bind(null,e)),th(function(){!(I&6)&&$t()}),n=null;else{switch(Vc(r)){case 1:n=Lo;break;case 4:n=$c;break;case 16:n=$s;break;case 536870912:n=Bc;break;default:n=$s}n=pf(n,af.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function af(e,t){if(Rs=-1,Ts=0,I&6)throw Error(b(327));var n=e.callbackNode;if(_n()&&e.callbackNode!==n)return null;var r=Bs(e,e===oe?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=al(e,r);else{t=r;var s=I;I|=2;var l=uf();(oe!==e||ue!==t)&&(ct=null,An=te()+500,Gt(e,t));do try{bh();break}catch(i){of(e,i)}while(!0);Wo(),rl.current=l,I=s,ne!==null?t=0:(oe=null,ue=0,t=se)}if(t!==0){if(t===2&&(s=Fa(e),s!==0&&(r=s,t=io(e,s))),t===1)throw n=Lr,Gt(e,0),bt(e,r),be(e,te()),n;if(t===6)bt(e,r);else{if(s=e.current.alternate,!(r&30)&&!Sh(s)&&(t=al(e,r),t===2&&(l=Fa(e),l!==0&&(r=l,t=io(e,l))),t===1))throw n=Lr,Gt(e,0),bt(e,r),be(e,te()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:qt(e,je,ct);break;case 3:if(bt(e,r),(r&130023424)===r&&(t=ai+500-te(),10<t)){if(Bs(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Ba(qt.bind(null,e,je,ct),t);break}qt(e,je,ct);break;case 4:if(bt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Je(r);l=1<<o,o=t[o],o>s&&(s=o),r&=~l}if(r=s,r=te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*kh(r/1960))-r,10<r){e.timeoutHandle=Ba(qt.bind(null,e,je,ct),r);break}qt(e,je,ct);break;case 5:qt(e,je,ct);break;default:throw Error(b(329))}}}return be(e,te()),e.callbackNode===n?af.bind(null,e):null}function io(e,t){var n=mr;return e.current.memoizedState.isDehydrated&&(Gt(e,t).flags|=256),e=al(e,t),e!==2&&(t=je,je=n,t!==null&&uo(t)),e}function uo(e){je===null?je=e:je.push.apply(je,e)}function Sh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],l=s.getSnapshot;s=s.value;try{if(!Ye(l(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function bt(e,t){for(t&=~li,t&=~jl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Je(t),r=1<<n;e[n]=-1,t&=~r}}function Ou(e){if(I&6)throw Error(b(327));_n();var t=Bs(e,0);if(!(t&1))return be(e,te()),null;var n=al(e,t);if(e.tag!==0&&n===2){var r=Fa(e);r!==0&&(t=r,n=io(e,r))}if(n===1)throw n=Lr,Gt(e,0),bt(e,t),be(e,te()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,qt(e,je,ct),be(e,te()),null}function oi(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(An=te()+500,yl&&$t())}}function ln(e){_t!==null&&_t.tag===0&&!(I&6)&&_n();var t=I;I|=1;var n=$e.transition,r=$;try{if($e.transition=null,$=1,e)return e()}finally{$=r,$e.transition=n,I=t,!(I&6)&&$t()}}function ii(){Re=jn.current,Q(jn)}function Gt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,eh(n)),ne!==null)for(n=ne.return;n!==null;){var r=n;switch(Bo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&qs();break;case 3:On(),Q(Se),Q(he),Yo();break;case 5:Xo(r);break;case 4:On();break;case 13:Q(X);break;case 19:Q(X);break;case 10:Qo(r.type._context);break;case 22:case 23:ii()}n=n.return}if(oe=e,ne=e=Dt(e.current,null),ue=Re=t,se=0,Lr=null,li=jl=sn=0,je=mr=null,Jt!==null){for(t=0;t<Jt.length;t++)if(n=Jt[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,l=n.pending;if(l!==null){var o=l.next;l.next=s,r.next=o}n.pending=r}Jt=null}return e}function of(e,t){do{var n=ne;try{if(Wo(),bs.current=nl,tl){for(var r=Y.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}tl=!1}if(rn=0,ae=re=Y=null,dr=!1,Rr=0,si.current=null,n===null||n.return===null){se=1,Lr=t,ne=null;break}e:{var l=e,o=n.return,i=n,u=t;if(t=ue,i.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,d=i,g=d.tag;if(!(d.mode&1)&&(g===0||g===11||g===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var w=wu(o);if(w!==null){w.flags&=-257,Nu(w,o,i,l,t),w.mode&1&&vu(l,c,t),t=w,u=c;var x=t.updateQueue;if(x===null){var y=new Set;y.add(u),t.updateQueue=y}else x.add(u);break e}else{if(!(t&1)){vu(l,c,t),ui();break e}u=Error(b(426))}}else if(q&&i.mode&1){var N=wu(o);if(N!==null){!(N.flags&65536)&&(N.flags|=256),Nu(N,o,i,l,t),Ho(Fn(u,i));break e}}l=u=Fn(u,i),se!==4&&(se=2),mr===null?mr=[l]:mr.push(l),l=o;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var h=Vd(l,u,t);mu(l,h);break e;case 1:i=u;var m=l.type,f=l.stateNode;if(!(l.flags&128)&&(typeof m.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Ft===null||!Ft.has(f)))){l.flags|=65536,t&=-t,l.lanes|=t;var k=Wd(l,i,t);mu(l,k);break e}}l=l.return}while(l!==null)}df(n)}catch(E){t=E,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(!0)}function uf(){var e=rl.current;return rl.current=nl,e===null?nl:e}function ui(){(se===0||se===3||se===2)&&(se=4),oe===null||!(sn&268435455)&&!(jl&268435455)||bt(oe,ue)}function al(e,t){var n=I;I|=2;var r=uf();(oe!==e||ue!==t)&&(ct=null,Gt(e,t));do try{Eh();break}catch(s){of(e,s)}while(!0);if(Wo(),I=n,rl.current=r,ne!==null)throw Error(b(261));return oe=null,ue=0,se}function Eh(){for(;ne!==null;)cf(ne)}function bh(){for(;ne!==null&&!Ym();)cf(ne)}function cf(e){var t=mf(e.alternate,e,Re);e.memoizedProps=e.pendingProps,t===null?df(e):ne=t,si.current=null}function df(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=vh(n,t),n!==null){n.flags&=32767,ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,ne=null;return}}else if(n=yh(n,t,Re),n!==null){ne=n;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);se===0&&(se=5)}function qt(e,t,n){var r=$,s=$e.transition;try{$e.transition=null,$=1,Ch(e,t,n,r)}finally{$e.transition=s,$=r}return null}function Ch(e,t,n,r){do _n();while(_t!==null);if(I&6)throw Error(b(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(op(e,l),e===oe&&(ne=oe=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ps||(ps=!0,pf($s,function(){return _n(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=$e.transition,$e.transition=null;var o=$;$=1;var i=I;I|=4,si.current=null,Nh(e,n),sf(n,e),qp(Ia),Hs=!!Ua,Ia=Ua=null,e.current=n,jh(n),Gm(),I=i,$=o,$e.transition=l}else e.current=n;if(ps&&(ps=!1,_t=e,ll=s),l=e.pendingLanes,l===0&&(Ft=null),tp(n.stateNode),be(e,te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(sl)throw sl=!1,e=ao,ao=null,e;return ll&1&&e.tag!==0&&_n(),l=e.pendingLanes,l&1?e===oo?pr++:(pr=0,oo=e):pr=0,$t(),null}function _n(){if(_t!==null){var e=Vc(ll),t=$e.transition,n=$;try{if($e.transition=null,$=16>e?16:e,_t===null)var r=!1;else{if(e=_t,_t=null,ll=0,I&6)throw Error(b(331));var s=I;for(I|=4,T=e.current;T!==null;){var l=T,o=l.child;if(T.flags&16){var i=l.deletions;if(i!==null){for(var u=0;u<i.length;u++){var c=i[u];for(T=c;T!==null;){var d=T;switch(d.tag){case 0:case 11:case 15:fr(8,d,l)}var g=d.child;if(g!==null)g.return=d,T=g;else for(;T!==null;){d=T;var p=d.sibling,w=d.return;if(tf(d),d===c){T=null;break}if(p!==null){p.return=w,T=p;break}T=w}}}var x=l.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var N=y.sibling;y.sibling=null,y=N}while(y!==null)}}T=l}}if(l.subtreeFlags&2064&&o!==null)o.return=l,T=o;else e:for(;T!==null;){if(l=T,l.flags&2048)switch(l.tag){case 0:case 11:case 15:fr(9,l,l.return)}var h=l.sibling;if(h!==null){h.return=l.return,T=h;break e}T=l.return}}var m=e.current;for(T=m;T!==null;){o=T;var f=o.child;if(o.subtreeFlags&2064&&f!==null)f.return=o,T=f;else e:for(o=m;T!==null;){if(i=T,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Nl(9,i)}}catch(E){Z(i,i.return,E)}if(i===o){T=null;break e}var k=i.sibling;if(k!==null){k.return=i.return,T=k;break e}T=i.return}}if(I=s,$t(),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(ml,e)}catch{}r=!0}return r}finally{$=n,$e.transition=t}}return!1}function Fu(e,t,n){t=Fn(n,t),t=Vd(e,t,1),e=Ot(e,t,1),t=xe(),e!==null&&(Ur(e,1,t),be(e,t))}function Z(e,t,n){if(e.tag===3)Fu(e,e,n);else for(;t!==null;){if(t.tag===3){Fu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ft===null||!Ft.has(r))){e=Fn(n,e),e=Wd(t,e,1),t=Ot(t,e,1),e=xe(),t!==null&&(Ur(t,1,e),be(t,e));break}}t=t.return}}function _h(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,oe===e&&(ue&n)===n&&(se===4||se===3&&(ue&130023424)===ue&&500>te()-ai?Gt(e,0):li|=n),be(e,t)}function ff(e,t){t===0&&(e.mode&1?(t=ss,ss<<=1,!(ss&130023424)&&(ss=4194304)):t=1);var n=xe();e=yt(e,t),e!==null&&(Ur(e,t,n),be(e,n))}function Rh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ff(e,n)}function Th(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),ff(e,n)}var mf;mf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Se.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,xh(e,t,n);ke=!!(e.flags&131072)}else ke=!1,q&&t.flags&1048576&&gd(t,Xs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;_s(e,t),e=t.pendingProps;var s=Tn(t,he.current);Cn(t,n),s=Zo(null,t,r,e,s,n);var l=ei();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ee(r)?(l=!0,Ks(t)):l=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Ko(t),s.updater=wl,t.stateNode=s,s._reactInternals=t,Ja(t,r,e,n),t=Ga(null,t,r,!0,l,n)):(t.tag=0,q&&l&&$o(t),ge(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(_s(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=Lh(r),e=Qe(r,e),s){case 0:t=Ya(null,t,r,e,n);break e;case 1:t=Su(null,t,r,e,n);break e;case 11:t=ju(null,t,r,e,n);break e;case 14:t=ku(null,t,r,Qe(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),Ya(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),Su(e,t,r,s,n);case 3:e:{if(Jd(t),e===null)throw Error(b(387));r=t.pendingProps,l=t.memoizedState,s=l.element,jd(e,t),Zs(t,r,null,n);var o=t.memoizedState;if(r=o.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){s=Fn(Error(b(423)),t),t=Eu(e,t,r,n,s);break e}else if(r!==s){s=Fn(Error(b(424)),t),t=Eu(e,t,r,n,s);break e}else for(Pe=Lt(t.stateNode.containerInfo.firstChild),Oe=t,q=!0,Ke=null,n=wd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Pn(),r===s){t=vt(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return kd(t),e===null&&Qa(t),r=t.type,s=t.pendingProps,l=e!==null?e.memoizedProps:null,o=s.children,$a(r,s)?o=null:l!==null&&$a(r,l)&&(t.flags|=32),Kd(e,t),ge(e,t,o,n),t.child;case 6:return e===null&&Qa(t),null;case 13:return Xd(e,t,n);case 4:return Jo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Ln(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),ju(e,t,r,s,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,l=t.memoizedProps,o=s.value,H(Ys,r._currentValue),r._currentValue=o,l!==null)if(Ye(l.value,o)){if(l.children===s.children&&!Se.current){t=vt(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var i=l.dependencies;if(i!==null){o=l.child;for(var u=i.firstContext;u!==null;){if(u.context===r){if(l.tag===1){u=pt(-1,n&-n),u.tag=2;var c=l.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}l.lanes|=n,u=l.alternate,u!==null&&(u.lanes|=n),qa(l.return,n,t),i.lanes|=n;break}u=u.next}}else if(l.tag===10)o=l.type===t.type?null:l.child;else if(l.tag===18){if(o=l.return,o===null)throw Error(b(341));o.lanes|=n,i=o.alternate,i!==null&&(i.lanes|=n),qa(o,n,t),o=l.sibling}else o=l.child;if(o!==null)o.return=l;else for(o=l;o!==null;){if(o===t){o=null;break}if(l=o.sibling,l!==null){l.return=o.return,o=l;break}o=o.return}l=o}ge(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Cn(t,n),s=Be(s),r=r(s),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,s=Qe(r,t.pendingProps),s=Qe(r.type,s),ku(e,t,r,s,n);case 15:return Qd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),_s(e,t),t.tag=1,Ee(r)?(e=!0,Ks(t)):e=!1,Cn(t,n),Hd(t,r,s),Ja(t,r,s,n),Ga(null,t,r,!0,e,n);case 19:return Yd(e,t,n);case 22:return qd(e,t,n)}throw Error(b(156,t.tag))};function pf(e,t){return Ic(e,t)}function Ph(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ie(e,t,n,r){return new Ph(e,t,n,r)}function ci(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lh(e){if(typeof e=="function")return ci(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ro)return 11;if(e===To)return 14}return 2}function Dt(e,t){var n=e.alternate;return n===null?(n=Ie(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ps(e,t,n,r,s,l){var o=2;if(r=e,typeof e=="function")ci(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case fn:return Zt(n.children,s,l,t);case _o:o=8,s|=8;break;case va:return e=Ie(12,n,t,s|2),e.elementType=va,e.lanes=l,e;case wa:return e=Ie(13,n,t,s),e.elementType=wa,e.lanes=l,e;case Na:return e=Ie(19,n,t,s),e.elementType=Na,e.lanes=l,e;case kc:return kl(n,s,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Nc:o=10;break e;case jc:o=9;break e;case Ro:o=11;break e;case To:o=14;break e;case kt:o=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=Ie(o,n,t,s),t.elementType=e,t.type=r,t.lanes=l,t}function Zt(e,t,n,r){return e=Ie(7,e,r,t),e.lanes=n,e}function kl(e,t,n,r){return e=Ie(22,e,r,t),e.elementType=kc,e.lanes=n,e.stateNode={isHidden:!1},e}function da(e,t,n){return e=Ie(6,e,null,t),e.lanes=n,e}function fa(e,t,n){return t=Ie(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Oh(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ql(0),this.expirationTimes=Ql(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ql(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function di(e,t,n,r,s,l,o,i,u){return e=new Oh(e,t,n,i,u),t===1?(t=1,l===!0&&(t|=8)):t=0,l=Ie(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ko(l),e}function Fh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:dn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function hf(e){if(!e)return zt;e=e._reactInternals;e:{if(un(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ee(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(Ee(n))return pd(e,n,t)}return t}function gf(e,t,n,r,s,l,o,i,u){return e=di(n,r,!0,e,s,l,o,i,u),e.context=hf(null),n=e.current,r=xe(),s=At(n),l=pt(r,s),l.callback=t??null,Ot(n,l,s),e.current.lanes=s,Ur(e,s,r),be(e,r),e}function Sl(e,t,n,r){var s=t.current,l=xe(),o=At(s);return n=hf(n),t.context===null?t.context=n:t.pendingContext=n,t=pt(l,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ot(s,t,o),e!==null&&(Xe(e,s,o,l),Es(e,s,o)),o}function ol(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Au(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function fi(e,t){Au(e,t),(e=e.alternate)&&Au(e,t)}function Ah(){return null}var xf=typeof reportError=="function"?reportError:function(e){console.error(e)};function mi(e){this._internalRoot=e}El.prototype.render=mi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));Sl(e,t,null,null)};El.prototype.unmount=mi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ln(function(){Sl(null,e,null,null)}),t[xt]=null}};function El(e){this._internalRoot=e}El.prototype.unstable_scheduleHydration=function(e){if(e){var t=qc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Et.length&&t!==0&&t<Et[n].priority;n++);Et.splice(n,0,e),n===0&&Jc(e)}};function pi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function bl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Du(){}function Dh(e,t,n,r,s){if(s){if(typeof r=="function"){var l=r;r=function(){var c=ol(o);l.call(c)}}var o=gf(t,r,e,0,null,!1,!1,"",Du);return e._reactRootContainer=o,e[xt]=o.current,Sr(e.nodeType===8?e.parentNode:e),ln(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var i=r;r=function(){var c=ol(u);i.call(c)}}var u=di(e,0,!1,null,null,!1,!1,"",Du);return e._reactRootContainer=u,e[xt]=u.current,Sr(e.nodeType===8?e.parentNode:e),ln(function(){Sl(t,u,n,r)}),u}function Cl(e,t,n,r,s){var l=n._reactRootContainer;if(l){var o=l;if(typeof s=="function"){var i=s;s=function(){var u=ol(o);i.call(u)}}Sl(t,o,e,s)}else o=Dh(n,t,e,s,r);return ol(o)}Wc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=sr(t.pendingLanes);n!==0&&(Oo(t,n|1),be(t,te()),!(I&6)&&(An=te()+500,$t()))}break;case 13:ln(function(){var r=yt(e,1);if(r!==null){var s=xe();Xe(r,e,1,s)}}),fi(e,1)}};Fo=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=xe();Xe(t,e,134217728,n)}fi(e,134217728)}};Qc=function(e){if(e.tag===13){var t=At(e),n=yt(e,t);if(n!==null){var r=xe();Xe(n,e,t,r)}fi(e,t)}};qc=function(){return $};Kc=function(e,t){var n=$;try{return $=e,t()}finally{$=n}};Pa=function(e,t,n){switch(t){case"input":if(Sa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=xl(r);if(!s)throw Error(b(90));Ec(r),Sa(r,s)}}}break;case"textarea":Cc(e,n);break;case"select":t=n.value,t!=null&&kn(e,!!n.multiple,t,!1)}};Fc=oi;Ac=ln;var Mh={usingClientEntryPoint:!1,Events:[$r,gn,xl,Lc,Oc,oi]},Zn={findFiberByHostInstance:Kt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},zh={bundleType:Zn.bundleType,version:Zn.version,rendererPackageName:Zn.rendererPackageName,rendererConfig:Zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Nt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zc(e),e===null?null:e.stateNode},findFiberByHostInstance:Zn.findFiberByHostInstance||Ah,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var hs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!hs.isDisabled&&hs.supportsFiber)try{ml=hs.inject(zh),it=hs}catch{}}Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mh;Ae.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!pi(t))throw Error(b(200));return Fh(e,t,null,n)};Ae.createRoot=function(e,t){if(!pi(e))throw Error(b(299));var n=!1,r="",s=xf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=di(e,1,!1,null,null,n,!1,r,s),e[xt]=t.current,Sr(e.nodeType===8?e.parentNode:e),new mi(t)};Ae.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=zc(t),e=e===null?null:e.stateNode,e};Ae.flushSync=function(e){return ln(e)};Ae.hydrate=function(e,t,n){if(!bl(t))throw Error(b(200));return Cl(null,e,t,!0,n)};Ae.hydrateRoot=function(e,t,n){if(!pi(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,s=!1,l="",o=xf;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=gf(t,null,e,1,n??null,s,!1,l,o),e[xt]=t.current,Sr(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new El(t)};Ae.render=function(e,t,n){if(!bl(t))throw Error(b(200));return Cl(null,e,t,!1,n)};Ae.unmountComponentAtNode=function(e){if(!bl(e))throw Error(b(40));return e._reactRootContainer?(ln(function(){Cl(null,null,e,!1,function(){e._reactRootContainer=null,e[xt]=null})}),!0):!1};Ae.unstable_batchedUpdates=oi;Ae.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!bl(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return Cl(e,t,n,!1,r)};Ae.version="18.3.1-next-f1338f8080-20240426";function yf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(yf)}catch(e){console.error(e)}}yf(),xc.exports=Ae;var Uh=xc.exports,vf,Mu=Uh;vf=Mu.createRoot,Mu.hydrateRoot;/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var zu="popstate";function Ih(e={}){function t(r,s){let{pathname:l,search:o,hash:i}=r.location;return co("",{pathname:l,search:o,hash:i},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:Or(s)}return Bh(t,n,null,e)}function K(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ge(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function $h(){return Math.random().toString(36).substring(2,10)}function Uu(e,t){return{usr:e.state,key:e.key,idx:t}}function co(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?$n(t):t,state:n,key:t&&t.key||r||$h()}}function Or({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function $n(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function Bh(e,t,n,r={}){let{window:s=document.defaultView,v5Compat:l=!1}=r,o=s.history,i="POP",u=null,c=d();c==null&&(c=0,o.replaceState({...o.state,idx:c},""));function d(){return(o.state||{idx:null}).idx}function g(){i="POP";let N=d(),h=N==null?null:N-c;c=N,u&&u({action:i,location:y.location,delta:h})}function p(N,h){i="PUSH";let m=co(y.location,N,h);c=d()+1;let f=Uu(m,c),k=y.createHref(m);try{o.pushState(f,"",k)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;s.location.assign(k)}l&&u&&u({action:i,location:y.location,delta:1})}function w(N,h){i="REPLACE";let m=co(y.location,N,h);c=d();let f=Uu(m,c),k=y.createHref(m);o.replaceState(f,"",k),l&&u&&u({action:i,location:y.location,delta:0})}function x(N){return Hh(N)}let y={get action(){return i},get location(){return e(s,o)},listen(N){if(u)throw new Error("A history only accepts one active listener");return s.addEventListener(zu,g),u=N,()=>{s.removeEventListener(zu,g),u=null}},createHref(N){return t(s,N)},createURL:x,encodeLocation(N){let h=x(N);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:p,replace:w,go(N){return o.go(N)}};return y}function Hh(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),K(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:Or(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function wf(e,t,n="/"){return Vh(e,t,n,!1)}function Vh(e,t,n,r){let s=typeof t=="string"?$n(t):t,l=wt(s.pathname||"/",n);if(l==null)return null;let o=Nf(e);Wh(o);let i=null;for(let u=0;i==null&&u<o.length;++u){let c=n0(l);i=e0(o[u],c,r)}return i}function Nf(e,t=[],n=[],r=""){let s=(l,o,i)=>{let u={relativePath:i===void 0?l.path||"":i,caseSensitive:l.caseSensitive===!0,childrenIndex:o,route:l};u.relativePath.startsWith("/")&&(K(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let c=ht([r,u.relativePath]),d=n.concat(u);l.children&&l.children.length>0&&(K(l.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),Nf(l.children,t,d,c)),!(l.path==null&&!l.index)&&t.push({path:c,score:Gh(c,l.index),routesMeta:d})};return e.forEach((l,o)=>{var i;if(l.path===""||!((i=l.path)!=null&&i.includes("?")))s(l,o);else for(let u of jf(l.path))s(l,o,u)}),t}function jf(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return s?[l,""]:[l];let o=jf(r.join("/")),i=[];return i.push(...o.map(u=>u===""?l:[l,u].join("/"))),s&&i.push(...o),i.map(u=>e.startsWith("/")&&u===""?"/":u)}function Wh(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Zh(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var Qh=/^:[\w-]+$/,qh=3,Kh=2,Jh=1,Xh=10,Yh=-2,Iu=e=>e==="*";function Gh(e,t){let n=e.split("/"),r=n.length;return n.some(Iu)&&(r+=Yh),t&&(r+=Kh),n.filter(s=>!Iu(s)).reduce((s,l)=>s+(Qh.test(l)?qh:l===""?Jh:Xh),r)}function Zh(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function e0(e,t,n=!1){let{routesMeta:r}=e,s={},l="/",o=[];for(let i=0;i<r.length;++i){let u=r[i],c=i===r.length-1,d=l==="/"?t:t.slice(l.length)||"/",g=il({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},d),p=u.route;if(!g&&c&&n&&!r[r.length-1].route.index&&(g=il({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},d)),!g)return null;Object.assign(s,g.params),o.push({params:s,pathname:ht([l,g.pathname]),pathnameBase:a0(ht([l,g.pathnameBase])),route:p}),g.pathnameBase!=="/"&&(l=ht([l,g.pathnameBase]))}return o}function il(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=t0(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let l=s[0],o=l.replace(/(.)\/+$/,"$1"),i=s.slice(1);return{params:r.reduce((c,{paramName:d,isOptional:g},p)=>{if(d==="*"){let x=i[p]||"";o=l.slice(0,l.length-x.length).replace(/(.)\/+$/,"$1")}const w=i[p];return g&&!w?c[d]=void 0:c[d]=(w||"").replace(/%2F/g,"/"),c},{}),pathname:l,pathnameBase:o,pattern:e}}function t0(e,t=!1,n=!0){Ge(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,i,u)=>(r.push({paramName:i,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function n0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ge(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function wt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function r0(e,t="/"){let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?$n(e):e;return{pathname:n?n.startsWith("/")?n:s0(n,t):t,search:o0(r),hash:i0(s)}}function s0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function ma(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function l0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function hi(e){let t=l0(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function gi(e,t,n,r=!1){let s;typeof e=="string"?s=$n(e):(s={...e},K(!s.pathname||!s.pathname.includes("?"),ma("?","pathname","search",s)),K(!s.pathname||!s.pathname.includes("#"),ma("#","pathname","hash",s)),K(!s.search||!s.search.includes("#"),ma("#","search","hash",s)));let l=e===""||s.pathname==="",o=l?"/":s.pathname,i;if(o==null)i=n;else{let g=t.length-1;if(!r&&o.startsWith("..")){let p=o.split("/");for(;p[0]==="..";)p.shift(),g-=1;s.pathname=p.join("/")}i=g>=0?t[g]:"/"}let u=r0(s,i),c=o&&o!=="/"&&o.endsWith("/"),d=(l||o===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||d)&&(u.pathname+="/"),u}var ht=e=>e.join("/").replace(/\/\/+/g,"/"),a0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),o0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,i0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function u0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var kf=["POST","PUT","PATCH","DELETE"];new Set(kf);var c0=["GET",...kf];new Set(c0);var Bn=v.createContext(null);Bn.displayName="DataRouter";var _l=v.createContext(null);_l.displayName="DataRouterState";v.createContext(!1);var Sf=v.createContext({isTransitioning:!1});Sf.displayName="ViewTransition";var d0=v.createContext(new Map);d0.displayName="Fetchers";var f0=v.createContext(null);f0.displayName="Await";var Ze=v.createContext(null);Ze.displayName="Navigation";var Hr=v.createContext(null);Hr.displayName="Location";var et=v.createContext({outlet:null,matches:[],isDataRoute:!1});et.displayName="Route";var xi=v.createContext(null);xi.displayName="RouteError";function m0(e,{relative:t}={}){K(Hn(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=v.useContext(Ze),{hash:s,pathname:l,search:o}=Wr(e,{relative:t}),i=l;return n!=="/"&&(i=l==="/"?n:ht([n,l])),r.createHref({pathname:i,search:o,hash:s})}function Hn(){return v.useContext(Hr)!=null}function Bt(){return K(Hn(),"useLocation() may be used only in the context of a <Router> component."),v.useContext(Hr).location}var Ef="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function bf(e){v.useContext(Ze).static||v.useLayoutEffect(e)}function Vr(){let{isDataRoute:e}=v.useContext(et);return e?C0():p0()}function p0(){K(Hn(),"useNavigate() may be used only in the context of a <Router> component.");let e=v.useContext(Bn),{basename:t,navigator:n}=v.useContext(Ze),{matches:r}=v.useContext(et),{pathname:s}=Bt(),l=JSON.stringify(hi(r)),o=v.useRef(!1);return bf(()=>{o.current=!0}),v.useCallback((u,c={})=>{if(Ge(o.current,Ef),!o.current)return;if(typeof u=="number"){n.go(u);return}let d=gi(u,JSON.parse(l),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:ht([t,d.pathname])),(c.replace?n.replace:n.push)(d,c.state,c)},[t,n,l,s,e])}v.createContext(null);function h0(){let{matches:e}=v.useContext(et),t=e[e.length-1];return t?t.params:{}}function Wr(e,{relative:t}={}){let{matches:n}=v.useContext(et),{pathname:r}=Bt(),s=JSON.stringify(hi(n));return v.useMemo(()=>gi(e,JSON.parse(s),r,t==="path"),[e,s,r,t])}function g0(e,t){return Cf(e,t)}function Cf(e,t,n,r){var h;K(Hn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=v.useContext(Ze),{matches:l}=v.useContext(et),o=l[l.length-1],i=o?o.params:{},u=o?o.pathname:"/",c=o?o.pathnameBase:"/",d=o&&o.route;{let m=d&&d.path||"";_f(u,!d||m.endsWith("*")||m.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${m}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${m}"> to <Route path="${m==="/"?"*":`${m}/*`}">.`)}let g=Bt(),p;if(t){let m=typeof t=="string"?$n(t):t;K(c==="/"||((h=m.pathname)==null?void 0:h.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${m.pathname}" was given in the \`location\` prop.`),p=m}else p=g;let w=p.pathname||"/",x=w;if(c!=="/"){let m=c.replace(/^\//,"").split("/");x="/"+w.replace(/^\//,"").split("/").slice(m.length).join("/")}let y=wf(e,{pathname:x});Ge(d||y!=null,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),Ge(y==null||y[y.length-1].route.element!==void 0||y[y.length-1].route.Component!==void 0||y[y.length-1].route.lazy!==void 0,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let N=N0(y&&y.map(m=>Object.assign({},m,{params:Object.assign({},i,m.params),pathname:ht([c,s.encodeLocation?s.encodeLocation(m.pathname).pathname:m.pathname]),pathnameBase:m.pathnameBase==="/"?c:ht([c,s.encodeLocation?s.encodeLocation(m.pathnameBase).pathname:m.pathnameBase])})),l,n,r);return t&&N?v.createElement(Hr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...p},navigationType:"POP"}},N):N}function x0(){let e=b0(),t=u0(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:r},l={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=v.createElement(v.Fragment,null,v.createElement("p",null,"💿 Hey developer 👋"),v.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",v.createElement("code",{style:l},"ErrorBoundary")," or"," ",v.createElement("code",{style:l},"errorElement")," prop on your route.")),v.createElement(v.Fragment,null,v.createElement("h2",null,"Unexpected Application Error!"),v.createElement("h3",{style:{fontStyle:"italic"}},t),n?v.createElement("pre",{style:s},n):null,o)}var y0=v.createElement(x0,null),v0=class extends v.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?v.createElement(et.Provider,{value:this.props.routeContext},v.createElement(xi.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function w0({routeContext:e,match:t,children:n}){let r=v.useContext(Bn);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),v.createElement(et.Provider,{value:e},n)}function N0(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=n==null?void 0:n.errors;if(l!=null){let u=s.findIndex(c=>c.route.id&&(l==null?void 0:l[c.route.id])!==void 0);K(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(l).join(",")}`),s=s.slice(0,Math.min(s.length,u+1))}let o=!1,i=-1;if(n)for(let u=0;u<s.length;u++){let c=s[u];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(i=u),c.route.id){let{loaderData:d,errors:g}=n,p=c.route.loader&&!d.hasOwnProperty(c.route.id)&&(!g||g[c.route.id]===void 0);if(c.route.lazy||p){o=!0,i>=0?s=s.slice(0,i+1):s=[s[0]];break}}}return s.reduceRight((u,c,d)=>{let g,p=!1,w=null,x=null;n&&(g=l&&c.route.id?l[c.route.id]:void 0,w=c.route.errorElement||y0,o&&(i<0&&d===0?(_f("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),p=!0,x=null):i===d&&(p=!0,x=c.route.hydrateFallbackElement||null)));let y=t.concat(s.slice(0,d+1)),N=()=>{let h;return g?h=w:p?h=x:c.route.Component?h=v.createElement(c.route.Component,null):c.route.element?h=c.route.element:h=u,v.createElement(w0,{match:c,routeContext:{outlet:u,matches:y,isDataRoute:n!=null},children:h})};return n&&(c.route.ErrorBoundary||c.route.errorElement||d===0)?v.createElement(v0,{location:n.location,revalidation:n.revalidation,component:w,error:g,children:N(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):N()},null)}function yi(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function j0(e){let t=v.useContext(Bn);return K(t,yi(e)),t}function k0(e){let t=v.useContext(_l);return K(t,yi(e)),t}function S0(e){let t=v.useContext(et);return K(t,yi(e)),t}function vi(e){let t=S0(e),n=t.matches[t.matches.length-1];return K(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function E0(){return vi("useRouteId")}function b0(){var r;let e=v.useContext(xi),t=k0("useRouteError"),n=vi("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function C0(){let{router:e}=j0("useNavigate"),t=vi("useNavigate"),n=v.useRef(!1);return bf(()=>{n.current=!0}),v.useCallback(async(s,l={})=>{Ge(n.current,Ef),n.current&&(typeof s=="number"?e.navigate(s):await e.navigate(s,{fromRouteId:t,...l}))},[e,t])}var $u={};function _f(e,t,n){!t&&!$u[e]&&($u[e]=!0,Ge(!1,n))}v.memo(_0);function _0({routes:e,future:t,state:n}){return Cf(e,void 0,n,t)}function wi({to:e,replace:t,state:n,relative:r}){K(Hn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:s}=v.useContext(Ze);Ge(!s,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:l}=v.useContext(et),{pathname:o}=Bt(),i=Vr(),u=gi(e,hi(l),o,r==="path"),c=JSON.stringify(u);return v.useEffect(()=>{i(JSON.parse(c),{replace:t,state:n,relative:r})},[i,c,r,t,n]),null}function Me(e){K(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function R0({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:s,static:l=!1}){K(!Hn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),i=v.useMemo(()=>({basename:o,navigator:s,static:l,future:{}}),[o,s,l]);typeof n=="string"&&(n=$n(n));let{pathname:u="/",search:c="",hash:d="",state:g=null,key:p="default"}=n,w=v.useMemo(()=>{let x=wt(u,o);return x==null?null:{location:{pathname:x,search:c,hash:d,state:g,key:p},navigationType:r}},[o,u,c,d,g,p,r]);return Ge(w!=null,`<Router basename="${o}"> is not able to match the URL "${u}${c}${d}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:v.createElement(Ze.Provider,{value:i},v.createElement(Hr.Provider,{children:t,value:w}))}function Rf({children:e,location:t}){return g0(fo(e),t)}function fo(e,t=[]){let n=[];return v.Children.forEach(e,(r,s)=>{if(!v.isValidElement(r))return;let l=[...t,s];if(r.type===v.Fragment){n.push.apply(n,fo(r.props.children,l));return}K(r.type===Me,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),K(!r.props.index||!r.props.children,"An index route cannot have child routes.");let o={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=fo(r.props.children,l)),n.push(o)}),n}var Ls="get",Os="application/x-www-form-urlencoded";function Rl(e){return e!=null&&typeof e.tagName=="string"}function T0(e){return Rl(e)&&e.tagName.toLowerCase()==="button"}function P0(e){return Rl(e)&&e.tagName.toLowerCase()==="form"}function L0(e){return Rl(e)&&e.tagName.toLowerCase()==="input"}function O0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function F0(e,t){return e.button===0&&(!t||t==="_self")&&!O0(e)}var gs=null;function A0(){if(gs===null)try{new FormData(document.createElement("form"),0),gs=!1}catch{gs=!0}return gs}var D0=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function pa(e){return e!=null&&!D0.has(e)?(Ge(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Os}"`),null):e}function M0(e,t){let n,r,s,l,o;if(P0(e)){let i=e.getAttribute("action");r=i?wt(i,t):null,n=e.getAttribute("method")||Ls,s=pa(e.getAttribute("enctype"))||Os,l=new FormData(e)}else if(T0(e)||L0(e)&&(e.type==="submit"||e.type==="image")){let i=e.form;if(i==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||i.getAttribute("action");if(r=u?wt(u,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||Ls,s=pa(e.getAttribute("formenctype"))||pa(i.getAttribute("enctype"))||Os,l=new FormData(i,e),!A0()){let{name:c,type:d,value:g}=e;if(d==="image"){let p=c?`${c}.`:"";l.append(`${p}x`,"0"),l.append(`${p}y`,"0")}else c&&l.append(c,g)}}else{if(Rl(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Ls,r=null,s=Os,o=e}return l&&s==="text/plain"&&(o=l,l=void 0),{action:r,method:n.toLowerCase(),encType:s,formData:l,body:o}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Ni(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function z0(e,t,n){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname=`_root.${n}`:t&&wt(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.${n}`:r.pathname=`${r.pathname.replace(/\/$/,"")}.${n}`,r}async function U0(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function I0(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function $0(e,t,n){let r=await Promise.all(e.map(async s=>{let l=t.routes[s.route.id];if(l){let o=await U0(l,n);return o.links?o.links():[]}return[]}));return W0(r.flat(1).filter(I0).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function Bu(e,t,n,r,s,l){let o=(u,c)=>n[c]?u.route.id!==n[c].route.id:!0,i=(u,c)=>{var d;return n[c].pathname!==u.pathname||((d=n[c].route.path)==null?void 0:d.endsWith("*"))&&n[c].params["*"]!==u.params["*"]};return l==="assets"?t.filter((u,c)=>o(u,c)||i(u,c)):l==="data"?t.filter((u,c)=>{var g;let d=r.routes[u.route.id];if(!d||!d.hasLoader)return!1;if(o(u,c)||i(u,c))return!0;if(u.route.shouldRevalidate){let p=u.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:((g=n[0])==null?void 0:g.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof p=="boolean")return p}return!0}):[]}function B0(e,t,{includeHydrateFallback:n}={}){return H0(e.map(r=>{let s=t.routes[r.route.id];if(!s)return[];let l=[s.module];return s.clientActionModule&&(l=l.concat(s.clientActionModule)),s.clientLoaderModule&&(l=l.concat(s.clientLoaderModule)),n&&s.hydrateFallbackModule&&(l=l.concat(s.hydrateFallbackModule)),s.imports&&(l=l.concat(s.imports)),l}).flat(1))}function H0(e){return[...new Set(e)]}function V0(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function W0(e,t){let n=new Set;return new Set(t),e.reduce((r,s)=>{let l=JSON.stringify(V0(s));return n.has(l)||(n.add(l),r.push({key:l,link:s})),r},[])}function Tf(){let e=v.useContext(Bn);return Ni(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Q0(){let e=v.useContext(_l);return Ni(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ji=v.createContext(void 0);ji.displayName="FrameworkContext";function Pf(){let e=v.useContext(ji);return Ni(e,"You must render this element inside a <HydratedRouter> element"),e}function q0(e,t){let n=v.useContext(ji),[r,s]=v.useState(!1),[l,o]=v.useState(!1),{onFocus:i,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:g}=t,p=v.useRef(null);v.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let y=h=>{h.forEach(m=>{o(m.isIntersecting)})},N=new IntersectionObserver(y,{threshold:.5});return p.current&&N.observe(p.current),()=>{N.disconnect()}}},[e]),v.useEffect(()=>{if(r){let y=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(y)}}},[r]);let w=()=>{s(!0)},x=()=>{s(!1),o(!1)};return n?e!=="intent"?[l,p,{}]:[l,p,{onFocus:er(i,w),onBlur:er(u,x),onMouseEnter:er(c,w),onMouseLeave:er(d,x),onTouchStart:er(g,w)}]:[!1,p,{}]}function er(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function K0({page:e,...t}){let{router:n}=Tf(),r=v.useMemo(()=>wf(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?v.createElement(X0,{page:e,matches:r,...t}):null}function J0(e){let{manifest:t,routeModules:n}=Pf(),[r,s]=v.useState([]);return v.useEffect(()=>{let l=!1;return $0(e,t,n).then(o=>{l||s(o)}),()=>{l=!0}},[e,t,n]),r}function X0({page:e,matches:t,...n}){let r=Bt(),{manifest:s,routeModules:l}=Pf(),{basename:o}=Tf(),{loaderData:i,matches:u}=Q0(),c=v.useMemo(()=>Bu(e,t,u,s,r,"data"),[e,t,u,s,r]),d=v.useMemo(()=>Bu(e,t,u,s,r,"assets"),[e,t,u,s,r]),g=v.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let x=new Set,y=!1;if(t.forEach(h=>{var f;let m=s.routes[h.route.id];!m||!m.hasLoader||(!c.some(k=>k.route.id===h.route.id)&&h.route.id in i&&((f=l[h.route.id])!=null&&f.shouldRevalidate)||m.hasClientLoader?y=!0:x.add(h.route.id))}),x.size===0)return[];let N=z0(e,o,"data");return y&&x.size>0&&N.searchParams.set("_routes",t.filter(h=>x.has(h.route.id)).map(h=>h.route.id).join(",")),[N.pathname+N.search]},[o,i,r,s,c,t,e,l]),p=v.useMemo(()=>B0(d,s),[d,s]),w=J0(d);return v.createElement(v.Fragment,null,g.map(x=>v.createElement("link",{key:x,rel:"prefetch",as:"fetch",href:x,...n})),p.map(x=>v.createElement("link",{key:x,rel:"modulepreload",href:x,...n})),w.map(({key:x,link:y})=>v.createElement("link",{key:x,...y})))}function Y0(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var Lf=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Lf&&(window.__reactRouterVersion="7.7.1")}catch{}function G0({basename:e,children:t,window:n}){let r=v.useRef();r.current==null&&(r.current=Ih({window:n,v5Compat:!0}));let s=r.current,[l,o]=v.useState({action:s.action,location:s.location}),i=v.useCallback(u=>{v.startTransition(()=>o(u))},[o]);return v.useLayoutEffect(()=>s.listen(i),[s,i]),v.createElement(R0,{basename:e,children:t,location:l.location,navigationType:l.action,navigator:s})}var Of=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ki=v.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:s,reloadDocument:l,replace:o,state:i,target:u,to:c,preventScrollReset:d,viewTransition:g,...p},w){let{basename:x}=v.useContext(Ze),y=typeof c=="string"&&Of.test(c),N,h=!1;if(typeof c=="string"&&y&&(N=c,Lf))try{let O=new URL(window.location.href),F=c.startsWith("//")?new URL(O.protocol+c):new URL(c),P=wt(F.pathname,x);F.origin===O.origin&&P!=null?c=P+F.search+F.hash:h=!0}catch{Ge(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let m=m0(c,{relative:s}),[f,k,E]=q0(r,p),C=tg(c,{replace:o,state:i,target:u,preventScrollReset:d,relative:s,viewTransition:g});function S(O){t&&t(O),O.defaultPrevented||C(O)}let R=v.createElement("a",{...p,...E,href:N||m,onClick:h||l?t:S,ref:Y0(w,k),target:u,"data-discover":!y&&n==="render"?"true":void 0});return f&&!y?v.createElement(v.Fragment,null,R,v.createElement(K0,{page:m})):R});ki.displayName="Link";var Ff=v.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:s=!1,style:l,to:o,viewTransition:i,children:u,...c},d){let g=Wr(o,{relative:c.relative}),p=Bt(),w=v.useContext(_l),{navigator:x,basename:y}=v.useContext(Ze),N=w!=null&&ag(g)&&i===!0,h=x.encodeLocation?x.encodeLocation(g).pathname:g.pathname,m=p.pathname,f=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;n||(m=m.toLowerCase(),f=f?f.toLowerCase():null,h=h.toLowerCase()),f&&y&&(f=wt(f,y)||f);const k=h!=="/"&&h.endsWith("/")?h.length-1:h.length;let E=m===h||!s&&m.startsWith(h)&&m.charAt(k)==="/",C=f!=null&&(f===h||!s&&f.startsWith(h)&&f.charAt(h.length)==="/"),S={isActive:E,isPending:C,isTransitioning:N},R=E?t:void 0,O;typeof r=="function"?O=r(S):O=[r,E?"active":null,C?"pending":null,N?"transitioning":null].filter(Boolean).join(" ");let F=typeof l=="function"?l(S):l;return v.createElement(ki,{...c,"aria-current":R,className:O,ref:d,style:F,to:o,viewTransition:i},typeof u=="function"?u(S):u)});Ff.displayName="NavLink";var Z0=v.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:s,state:l,method:o=Ls,action:i,onSubmit:u,relative:c,preventScrollReset:d,viewTransition:g,...p},w)=>{let x=sg(),y=lg(i,{relative:c}),N=o.toLowerCase()==="get"?"get":"post",h=typeof i=="string"&&Of.test(i),m=f=>{if(u&&u(f),f.defaultPrevented)return;f.preventDefault();let k=f.nativeEvent.submitter,E=(k==null?void 0:k.getAttribute("formmethod"))||o;x(k||f.currentTarget,{fetcherKey:t,method:E,navigate:n,replace:s,state:l,relative:c,preventScrollReset:d,viewTransition:g})};return v.createElement("form",{ref:w,method:N,action:y,onSubmit:r?u:m,...p,"data-discover":!h&&e==="render"?"true":void 0})});Z0.displayName="Form";function eg(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Af(e){let t=v.useContext(Bn);return K(t,eg(e)),t}function tg(e,{target:t,replace:n,state:r,preventScrollReset:s,relative:l,viewTransition:o}={}){let i=Vr(),u=Bt(),c=Wr(e,{relative:l});return v.useCallback(d=>{if(F0(d,t)){d.preventDefault();let g=n!==void 0?n:Or(u)===Or(c);i(e,{replace:g,state:r,preventScrollReset:s,relative:l,viewTransition:o})}},[u,i,c,n,r,t,e,s,l,o])}var ng=0,rg=()=>`__${String(++ng)}__`;function sg(){let{router:e}=Af("useSubmit"),{basename:t}=v.useContext(Ze),n=E0();return v.useCallback(async(r,s={})=>{let{action:l,method:o,encType:i,formData:u,body:c}=M0(r,t);if(s.navigate===!1){let d=s.fetcherKey||rg();await e.fetch(d,n,s.action||l,{preventScrollReset:s.preventScrollReset,formData:u,body:c,formMethod:s.method||o,formEncType:s.encType||i,flushSync:s.flushSync})}else await e.navigate(s.action||l,{preventScrollReset:s.preventScrollReset,formData:u,body:c,formMethod:s.method||o,formEncType:s.encType||i,replace:s.replace,state:s.state,fromRouteId:n,flushSync:s.flushSync,viewTransition:s.viewTransition})},[e,t,n])}function lg(e,{relative:t}={}){let{basename:n}=v.useContext(Ze),r=v.useContext(et);K(r,"useFormAction must be used inside a RouteContext");let[s]=r.matches.slice(-1),l={...Wr(e||".",{relative:t})},o=Bt();if(e==null){l.search=o.search;let i=new URLSearchParams(l.search),u=i.getAll("index");if(u.some(d=>d==="")){i.delete("index"),u.filter(g=>g).forEach(g=>i.append("index",g));let d=i.toString();l.search=d?`?${d}`:""}}return(!e||e===".")&&s.route.index&&(l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(l.pathname=l.pathname==="/"?n:ht([n,l.pathname])),Or(l)}function ag(e,{relative:t}={}){let n=v.useContext(Sf);K(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Af("useViewTransitionState"),s=Wr(e,{relative:t});if(!n.isTransitioning)return!1;let l=wt(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=wt(n.nextLocation.pathname,r)||n.nextLocation.pathname;return il(s.pathname,o)!=null||il(s.pathname,l)!=null}function Df(e,t){return function(){return e.apply(t,arguments)}}const{toString:og}=Object.prototype,{getPrototypeOf:Si}=Object,{iterator:Tl,toStringTag:Mf}=Symbol,Pl=(e=>t=>{const n=og.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),tt=e=>(e=e.toLowerCase(),t=>Pl(t)===e),Ll=e=>t=>typeof t===e,{isArray:Vn}=Array,Fr=Ll("undefined");function Qr(e){return e!==null&&!Fr(e)&&e.constructor!==null&&!Fr(e.constructor)&&Ce(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const zf=tt("ArrayBuffer");function ig(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&zf(e.buffer),t}const ug=Ll("string"),Ce=Ll("function"),Uf=Ll("number"),qr=e=>e!==null&&typeof e=="object",cg=e=>e===!0||e===!1,Fs=e=>{if(Pl(e)!=="object")return!1;const t=Si(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Mf in e)&&!(Tl in e)},dg=e=>{if(!qr(e)||Qr(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},fg=tt("Date"),mg=tt("File"),pg=tt("Blob"),hg=tt("FileList"),gg=e=>qr(e)&&Ce(e.pipe),xg=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ce(e.append)&&((t=Pl(e))==="formdata"||t==="object"&&Ce(e.toString)&&e.toString()==="[object FormData]"))},yg=tt("URLSearchParams"),[vg,wg,Ng,jg]=["ReadableStream","Request","Response","Headers"].map(tt),kg=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Kr(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),Vn(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{if(Qr(e))return;const l=n?Object.getOwnPropertyNames(e):Object.keys(e),o=l.length;let i;for(r=0;r<o;r++)i=l[r],t.call(null,e[i],i,e)}}function If(e,t){if(Qr(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const Yt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,$f=e=>!Fr(e)&&e!==Yt;function mo(){const{caseless:e}=$f(this)&&this||{},t={},n=(r,s)=>{const l=e&&If(t,s)||s;Fs(t[l])&&Fs(r)?t[l]=mo(t[l],r):Fs(r)?t[l]=mo({},r):Vn(r)?t[l]=r.slice():t[l]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&Kr(arguments[r],n);return t}const Sg=(e,t,n,{allOwnKeys:r}={})=>(Kr(t,(s,l)=>{n&&Ce(s)?e[l]=Df(s,n):e[l]=s},{allOwnKeys:r}),e),Eg=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),bg=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Cg=(e,t,n,r)=>{let s,l,o;const i={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),l=s.length;l-- >0;)o=s[l],(!r||r(o,e,t))&&!i[o]&&(t[o]=e[o],i[o]=!0);e=n!==!1&&Si(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},_g=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Rg=e=>{if(!e)return null;if(Vn(e))return e;let t=e.length;if(!Uf(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Tg=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Si(Uint8Array)),Pg=(e,t)=>{const r=(e&&e[Tl]).call(e);let s;for(;(s=r.next())&&!s.done;){const l=s.value;t.call(e,l[0],l[1])}},Lg=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Og=tt("HTMLFormElement"),Fg=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Hu=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Ag=tt("RegExp"),Bf=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Kr(n,(s,l)=>{let o;(o=t(s,l,e))!==!1&&(r[l]=o||s)}),Object.defineProperties(e,r)},Dg=e=>{Bf(e,(t,n)=>{if(Ce(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Ce(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Mg=(e,t)=>{const n={},r=s=>{s.forEach(l=>{n[l]=!0})};return Vn(e)?r(e):r(String(e).split(t)),n},zg=()=>{},Ug=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Ig(e){return!!(e&&Ce(e.append)&&e[Mf]==="FormData"&&e[Tl])}const $g=e=>{const t=new Array(10),n=(r,s)=>{if(qr(r)){if(t.indexOf(r)>=0)return;if(Qr(r))return r;if(!("toJSON"in r)){t[s]=r;const l=Vn(r)?[]:{};return Kr(r,(o,i)=>{const u=n(o,s+1);!Fr(u)&&(l[i]=u)}),t[s]=void 0,l}}return r};return n(e,0)},Bg=tt("AsyncFunction"),Hg=e=>e&&(qr(e)||Ce(e))&&Ce(e.then)&&Ce(e.catch),Hf=((e,t)=>e?setImmediate:t?((n,r)=>(Yt.addEventListener("message",({source:s,data:l})=>{s===Yt&&l===n&&r.length&&r.shift()()},!1),s=>{r.push(s),Yt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ce(Yt.postMessage)),Vg=typeof queueMicrotask<"u"?queueMicrotask.bind(Yt):typeof process<"u"&&process.nextTick||Hf,Wg=e=>e!=null&&Ce(e[Tl]),j={isArray:Vn,isArrayBuffer:zf,isBuffer:Qr,isFormData:xg,isArrayBufferView:ig,isString:ug,isNumber:Uf,isBoolean:cg,isObject:qr,isPlainObject:Fs,isEmptyObject:dg,isReadableStream:vg,isRequest:wg,isResponse:Ng,isHeaders:jg,isUndefined:Fr,isDate:fg,isFile:mg,isBlob:pg,isRegExp:Ag,isFunction:Ce,isStream:gg,isURLSearchParams:yg,isTypedArray:Tg,isFileList:hg,forEach:Kr,merge:mo,extend:Sg,trim:kg,stripBOM:Eg,inherits:bg,toFlatObject:Cg,kindOf:Pl,kindOfTest:tt,endsWith:_g,toArray:Rg,forEachEntry:Pg,matchAll:Lg,isHTMLForm:Og,hasOwnProperty:Hu,hasOwnProp:Hu,reduceDescriptors:Bf,freezeMethods:Dg,toObjectSet:Mg,toCamelCase:Fg,noop:zg,toFiniteNumber:Ug,findKey:If,global:Yt,isContextDefined:$f,isSpecCompliantForm:Ig,toJSONObject:$g,isAsyncFn:Bg,isThenable:Hg,setImmediate:Hf,asap:Vg,isIterable:Wg};function D(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}j.inherits(D,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:j.toJSONObject(this.config),code:this.code,status:this.status}}});const Vf=D.prototype,Wf={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Wf[e]={value:e}});Object.defineProperties(D,Wf);Object.defineProperty(Vf,"isAxiosError",{value:!0});D.from=(e,t,n,r,s,l)=>{const o=Object.create(Vf);return j.toFlatObject(e,o,function(u){return u!==Error.prototype},i=>i!=="isAxiosError"),D.call(o,e.message,t,n,r,s),o.cause=e,o.name=e.name,l&&Object.assign(o,l),o};const Qg=null;function po(e){return j.isPlainObject(e)||j.isArray(e)}function Qf(e){return j.endsWith(e,"[]")?e.slice(0,-2):e}function Vu(e,t,n){return e?e.concat(t).map(function(s,l){return s=Qf(s),!n&&l?"["+s+"]":s}).join(n?".":""):t}function qg(e){return j.isArray(e)&&!e.some(po)}const Kg=j.toFlatObject(j,{},null,function(t){return/^is[A-Z]/.test(t)});function Ol(e,t,n){if(!j.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=j.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,N){return!j.isUndefined(N[y])});const r=n.metaTokens,s=n.visitor||d,l=n.dots,o=n.indexes,u=(n.Blob||typeof Blob<"u"&&Blob)&&j.isSpecCompliantForm(t);if(!j.isFunction(s))throw new TypeError("visitor must be a function");function c(x){if(x===null)return"";if(j.isDate(x))return x.toISOString();if(j.isBoolean(x))return x.toString();if(!u&&j.isBlob(x))throw new D("Blob is not supported. Use a Buffer instead.");return j.isArrayBuffer(x)||j.isTypedArray(x)?u&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function d(x,y,N){let h=x;if(x&&!N&&typeof x=="object"){if(j.endsWith(y,"{}"))y=r?y:y.slice(0,-2),x=JSON.stringify(x);else if(j.isArray(x)&&qg(x)||(j.isFileList(x)||j.endsWith(y,"[]"))&&(h=j.toArray(x)))return y=Qf(y),h.forEach(function(f,k){!(j.isUndefined(f)||f===null)&&t.append(o===!0?Vu([y],k,l):o===null?y:y+"[]",c(f))}),!1}return po(x)?!0:(t.append(Vu(N,y,l),c(x)),!1)}const g=[],p=Object.assign(Kg,{defaultVisitor:d,convertValue:c,isVisitable:po});function w(x,y){if(!j.isUndefined(x)){if(g.indexOf(x)!==-1)throw Error("Circular reference detected in "+y.join("."));g.push(x),j.forEach(x,function(h,m){(!(j.isUndefined(h)||h===null)&&s.call(t,h,j.isString(m)?m.trim():m,y,p))===!0&&w(h,y?y.concat(m):[m])}),g.pop()}}if(!j.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Wu(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Ei(e,t){this._pairs=[],e&&Ol(e,this,t)}const qf=Ei.prototype;qf.append=function(t,n){this._pairs.push([t,n])};qf.toString=function(t){const n=t?function(r){return t.call(this,r,Wu)}:Wu;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Jg(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Kf(e,t,n){if(!t)return e;const r=n&&n.encode||Jg;j.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let l;if(s?l=s(t,n):l=j.isURLSearchParams(t)?t.toString():new Ei(t,n).toString(r),l){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+l}return e}class Qu{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){j.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Jf={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Xg=typeof URLSearchParams<"u"?URLSearchParams:Ei,Yg=typeof FormData<"u"?FormData:null,Gg=typeof Blob<"u"?Blob:null,Zg={isBrowser:!0,classes:{URLSearchParams:Xg,FormData:Yg,Blob:Gg},protocols:["http","https","file","blob","url","data"]},bi=typeof window<"u"&&typeof document<"u",ho=typeof navigator=="object"&&navigator||void 0,ex=bi&&(!ho||["ReactNative","NativeScript","NS"].indexOf(ho.product)<0),tx=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",nx=bi&&window.location.href||"http://localhost",rx=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:bi,hasStandardBrowserEnv:ex,hasStandardBrowserWebWorkerEnv:tx,navigator:ho,origin:nx},Symbol.toStringTag,{value:"Module"})),pe={...rx,...Zg};function sx(e,t){return Ol(e,new pe.classes.URLSearchParams,{visitor:function(n,r,s,l){return pe.isNode&&j.isBuffer(n)?(this.append(r,n.toString("base64")),!1):l.defaultVisitor.apply(this,arguments)},...t})}function lx(e){return j.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ax(e){const t={},n=Object.keys(e);let r;const s=n.length;let l;for(r=0;r<s;r++)l=n[r],t[l]=e[l];return t}function Xf(e){function t(n,r,s,l){let o=n[l++];if(o==="__proto__")return!0;const i=Number.isFinite(+o),u=l>=n.length;return o=!o&&j.isArray(s)?s.length:o,u?(j.hasOwnProp(s,o)?s[o]=[s[o],r]:s[o]=r,!i):((!s[o]||!j.isObject(s[o]))&&(s[o]=[]),t(n,r,s[o],l)&&j.isArray(s[o])&&(s[o]=ax(s[o])),!i)}if(j.isFormData(e)&&j.isFunction(e.entries)){const n={};return j.forEachEntry(e,(r,s)=>{t(lx(r),s,n,0)}),n}return null}function ox(e,t,n){if(j.isString(e))try{return(t||JSON.parse)(e),j.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Jr={transitional:Jf,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,l=j.isObject(t);if(l&&j.isHTMLForm(t)&&(t=new FormData(t)),j.isFormData(t))return s?JSON.stringify(Xf(t)):t;if(j.isArrayBuffer(t)||j.isBuffer(t)||j.isStream(t)||j.isFile(t)||j.isBlob(t)||j.isReadableStream(t))return t;if(j.isArrayBufferView(t))return t.buffer;if(j.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(l){if(r.indexOf("application/x-www-form-urlencoded")>-1)return sx(t,this.formSerializer).toString();if((i=j.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return Ol(i?{"files[]":t}:t,u&&new u,this.formSerializer)}}return l||s?(n.setContentType("application/json",!1),ox(t)):t}],transformResponse:[function(t){const n=this.transitional||Jr.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(j.isResponse(t)||j.isReadableStream(t))return t;if(t&&j.isString(t)&&(r&&!this.responseType||s)){const o=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(i){if(o)throw i.name==="SyntaxError"?D.from(i,D.ERR_BAD_RESPONSE,this,null,this.response):i}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:pe.classes.FormData,Blob:pe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};j.forEach(["delete","get","head","post","put","patch"],e=>{Jr.headers[e]={}});const ix=j.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ux=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(o){s=o.indexOf(":"),n=o.substring(0,s).trim().toLowerCase(),r=o.substring(s+1).trim(),!(!n||t[n]&&ix[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},qu=Symbol("internals");function tr(e){return e&&String(e).trim().toLowerCase()}function As(e){return e===!1||e==null?e:j.isArray(e)?e.map(As):String(e)}function cx(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const dx=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ha(e,t,n,r,s){if(j.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!j.isString(t)){if(j.isString(r))return t.indexOf(r)!==-1;if(j.isRegExp(r))return r.test(t)}}function fx(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function mx(e,t){const n=j.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,l,o){return this[r].call(this,t,s,l,o)},configurable:!0})})}let _e=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function l(i,u,c){const d=tr(u);if(!d)throw new Error("header name must be a non-empty string");const g=j.findKey(s,d);(!g||s[g]===void 0||c===!0||c===void 0&&s[g]!==!1)&&(s[g||u]=As(i))}const o=(i,u)=>j.forEach(i,(c,d)=>l(c,d,u));if(j.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(j.isString(t)&&(t=t.trim())&&!dx(t))o(ux(t),n);else if(j.isObject(t)&&j.isIterable(t)){let i={},u,c;for(const d of t){if(!j.isArray(d))throw TypeError("Object iterator must return a key-value pair");i[c=d[0]]=(u=i[c])?j.isArray(u)?[...u,d[1]]:[u,d[1]]:d[1]}o(i,n)}else t!=null&&l(n,t,r);return this}get(t,n){if(t=tr(t),t){const r=j.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return cx(s);if(j.isFunction(n))return n.call(this,s,r);if(j.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=tr(t),t){const r=j.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ha(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function l(o){if(o=tr(o),o){const i=j.findKey(r,o);i&&(!n||ha(r,r[i],i,n))&&(delete r[i],s=!0)}}return j.isArray(t)?t.forEach(l):l(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const l=n[r];(!t||ha(this,this[l],l,t,!0))&&(delete this[l],s=!0)}return s}normalize(t){const n=this,r={};return j.forEach(this,(s,l)=>{const o=j.findKey(r,l);if(o){n[o]=As(s),delete n[l];return}const i=t?fx(l):String(l).trim();i!==l&&delete n[l],n[i]=As(s),r[i]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return j.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&j.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[qu]=this[qu]={accessors:{}}).accessors,s=this.prototype;function l(o){const i=tr(o);r[i]||(mx(s,o),r[i]=!0)}return j.isArray(t)?t.forEach(l):l(t),this}};_e.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);j.reduceDescriptors(_e.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});j.freezeMethods(_e);function ga(e,t){const n=this||Jr,r=t||n,s=_e.from(r.headers);let l=r.data;return j.forEach(e,function(i){l=i.call(n,l,s.normalize(),t?t.status:void 0)}),s.normalize(),l}function Yf(e){return!!(e&&e.__CANCEL__)}function Wn(e,t,n){D.call(this,e??"canceled",D.ERR_CANCELED,t,n),this.name="CanceledError"}j.inherits(Wn,D,{__CANCEL__:!0});function Gf(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new D("Request failed with status code "+n.status,[D.ERR_BAD_REQUEST,D.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function px(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function hx(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,l=0,o;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),d=r[l];o||(o=c),n[s]=u,r[s]=c;let g=l,p=0;for(;g!==s;)p+=n[g++],g=g%e;if(s=(s+1)%e,s===l&&(l=(l+1)%e),c-o<t)return;const w=d&&c-d;return w?Math.round(p*1e3/w):void 0}}function gx(e,t){let n=0,r=1e3/t,s,l;const o=(c,d=Date.now())=>{n=d,s=null,l&&(clearTimeout(l),l=null),e(...c)};return[(...c)=>{const d=Date.now(),g=d-n;g>=r?o(c,d):(s=c,l||(l=setTimeout(()=>{l=null,o(s)},r-g)))},()=>s&&o(s)]}const ul=(e,t,n=3)=>{let r=0;const s=hx(50,250);return gx(l=>{const o=l.loaded,i=l.lengthComputable?l.total:void 0,u=o-r,c=s(u),d=o<=i;r=o;const g={loaded:o,total:i,progress:i?o/i:void 0,bytes:u,rate:c||void 0,estimated:c&&i&&d?(i-o)/c:void 0,event:l,lengthComputable:i!=null,[t?"download":"upload"]:!0};e(g)},n)},Ku=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ju=e=>(...t)=>j.asap(()=>e(...t)),xx=pe.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,pe.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(pe.origin),pe.navigator&&/(msie|trident)/i.test(pe.navigator.userAgent)):()=>!0,yx=pe.hasStandardBrowserEnv?{write(e,t,n,r,s,l){const o=[e+"="+encodeURIComponent(t)];j.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),j.isString(r)&&o.push("path="+r),j.isString(s)&&o.push("domain="+s),l===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function vx(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function wx(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Zf(e,t,n){let r=!vx(t);return e&&(r||n==!1)?wx(e,t):t}const Xu=e=>e instanceof _e?{...e}:e;function an(e,t){t=t||{};const n={};function r(c,d,g,p){return j.isPlainObject(c)&&j.isPlainObject(d)?j.merge.call({caseless:p},c,d):j.isPlainObject(d)?j.merge({},d):j.isArray(d)?d.slice():d}function s(c,d,g,p){if(j.isUndefined(d)){if(!j.isUndefined(c))return r(void 0,c,g,p)}else return r(c,d,g,p)}function l(c,d){if(!j.isUndefined(d))return r(void 0,d)}function o(c,d){if(j.isUndefined(d)){if(!j.isUndefined(c))return r(void 0,c)}else return r(void 0,d)}function i(c,d,g){if(g in t)return r(c,d);if(g in e)return r(void 0,c)}const u={url:l,method:l,data:l,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:i,headers:(c,d,g)=>s(Xu(c),Xu(d),g,!0)};return j.forEach(Object.keys({...e,...t}),function(d){const g=u[d]||s,p=g(e[d],t[d],d);j.isUndefined(p)&&g!==i||(n[d]=p)}),n}const em=e=>{const t=an({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:l,headers:o,auth:i}=t;t.headers=o=_e.from(o),t.url=Kf(Zf(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),i&&o.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):"")));let u;if(j.isFormData(n)){if(pe.hasStandardBrowserEnv||pe.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((u=o.getContentType())!==!1){const[c,...d]=u?u.split(";").map(g=>g.trim()).filter(Boolean):[];o.setContentType([c||"multipart/form-data",...d].join("; "))}}if(pe.hasStandardBrowserEnv&&(r&&j.isFunction(r)&&(r=r(t)),r||r!==!1&&xx(t.url))){const c=s&&l&&yx.read(l);c&&o.set(s,c)}return t},Nx=typeof XMLHttpRequest<"u",jx=Nx&&function(e){return new Promise(function(n,r){const s=em(e);let l=s.data;const o=_e.from(s.headers).normalize();let{responseType:i,onUploadProgress:u,onDownloadProgress:c}=s,d,g,p,w,x;function y(){w&&w(),x&&x(),s.cancelToken&&s.cancelToken.unsubscribe(d),s.signal&&s.signal.removeEventListener("abort",d)}let N=new XMLHttpRequest;N.open(s.method.toUpperCase(),s.url,!0),N.timeout=s.timeout;function h(){if(!N)return;const f=_e.from("getAllResponseHeaders"in N&&N.getAllResponseHeaders()),E={data:!i||i==="text"||i==="json"?N.responseText:N.response,status:N.status,statusText:N.statusText,headers:f,config:e,request:N};Gf(function(S){n(S),y()},function(S){r(S),y()},E),N=null}"onloadend"in N?N.onloadend=h:N.onreadystatechange=function(){!N||N.readyState!==4||N.status===0&&!(N.responseURL&&N.responseURL.indexOf("file:")===0)||setTimeout(h)},N.onabort=function(){N&&(r(new D("Request aborted",D.ECONNABORTED,e,N)),N=null)},N.onerror=function(){r(new D("Network Error",D.ERR_NETWORK,e,N)),N=null},N.ontimeout=function(){let k=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const E=s.transitional||Jf;s.timeoutErrorMessage&&(k=s.timeoutErrorMessage),r(new D(k,E.clarifyTimeoutError?D.ETIMEDOUT:D.ECONNABORTED,e,N)),N=null},l===void 0&&o.setContentType(null),"setRequestHeader"in N&&j.forEach(o.toJSON(),function(k,E){N.setRequestHeader(E,k)}),j.isUndefined(s.withCredentials)||(N.withCredentials=!!s.withCredentials),i&&i!=="json"&&(N.responseType=s.responseType),c&&([p,x]=ul(c,!0),N.addEventListener("progress",p)),u&&N.upload&&([g,w]=ul(u),N.upload.addEventListener("progress",g),N.upload.addEventListener("loadend",w)),(s.cancelToken||s.signal)&&(d=f=>{N&&(r(!f||f.type?new Wn(null,e,N):f),N.abort(),N=null)},s.cancelToken&&s.cancelToken.subscribe(d),s.signal&&(s.signal.aborted?d():s.signal.addEventListener("abort",d)));const m=px(s.url);if(m&&pe.protocols.indexOf(m)===-1){r(new D("Unsupported protocol "+m+":",D.ERR_BAD_REQUEST,e));return}N.send(l||null)})},kx=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const l=function(c){if(!s){s=!0,i();const d=c instanceof Error?c:this.reason;r.abort(d instanceof D?d:new Wn(d instanceof Error?d.message:d))}};let o=t&&setTimeout(()=>{o=null,l(new D(`timeout ${t} of ms exceeded`,D.ETIMEDOUT))},t);const i=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(l):c.removeEventListener("abort",l)}),e=null)};e.forEach(c=>c.addEventListener("abort",l));const{signal:u}=r;return u.unsubscribe=()=>j.asap(i),u}},Sx=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},Ex=async function*(e,t){for await(const n of bx(e))yield*Sx(n,t)},bx=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Yu=(e,t,n,r)=>{const s=Ex(e,t);let l=0,o,i=u=>{o||(o=!0,r&&r(u))};return new ReadableStream({async pull(u){try{const{done:c,value:d}=await s.next();if(c){i(),u.close();return}let g=d.byteLength;if(n){let p=l+=g;n(p)}u.enqueue(new Uint8Array(d))}catch(c){throw i(c),c}},cancel(u){return i(u),s.return()}},{highWaterMark:2})},Fl=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",tm=Fl&&typeof ReadableStream=="function",Cx=Fl&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),nm=(e,...t)=>{try{return!!e(...t)}catch{return!1}},_x=tm&&nm(()=>{let e=!1;const t=new Request(pe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Gu=64*1024,go=tm&&nm(()=>j.isReadableStream(new Response("").body)),cl={stream:go&&(e=>e.body)};Fl&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!cl[t]&&(cl[t]=j.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new D(`Response type '${t}' is not supported`,D.ERR_NOT_SUPPORT,r)})})})(new Response);const Rx=async e=>{if(e==null)return 0;if(j.isBlob(e))return e.size;if(j.isSpecCompliantForm(e))return(await new Request(pe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(j.isArrayBufferView(e)||j.isArrayBuffer(e))return e.byteLength;if(j.isURLSearchParams(e)&&(e=e+""),j.isString(e))return(await Cx(e)).byteLength},Tx=async(e,t)=>{const n=j.toFiniteNumber(e.getContentLength());return n??Rx(t)},Px=Fl&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:l,timeout:o,onDownloadProgress:i,onUploadProgress:u,responseType:c,headers:d,withCredentials:g="same-origin",fetchOptions:p}=em(e);c=c?(c+"").toLowerCase():"text";let w=kx([s,l&&l.toAbortSignal()],o),x;const y=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let N;try{if(u&&_x&&n!=="get"&&n!=="head"&&(N=await Tx(d,r))!==0){let E=new Request(t,{method:"POST",body:r,duplex:"half"}),C;if(j.isFormData(r)&&(C=E.headers.get("content-type"))&&d.setContentType(C),E.body){const[S,R]=Ku(N,ul(Ju(u)));r=Yu(E.body,Gu,S,R)}}j.isString(g)||(g=g?"include":"omit");const h="credentials"in Request.prototype;x=new Request(t,{...p,signal:w,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:h?g:void 0});let m=await fetch(x,p);const f=go&&(c==="stream"||c==="response");if(go&&(i||f&&y)){const E={};["status","statusText","headers"].forEach(O=>{E[O]=m[O]});const C=j.toFiniteNumber(m.headers.get("content-length")),[S,R]=i&&Ku(C,ul(Ju(i),!0))||[];m=new Response(Yu(m.body,Gu,S,()=>{R&&R(),y&&y()}),E)}c=c||"text";let k=await cl[j.findKey(cl,c)||"text"](m,e);return!f&&y&&y(),await new Promise((E,C)=>{Gf(E,C,{data:k,headers:_e.from(m.headers),status:m.status,statusText:m.statusText,config:e,request:x})})}catch(h){throw y&&y(),h&&h.name==="TypeError"&&/Load failed|fetch/i.test(h.message)?Object.assign(new D("Network Error",D.ERR_NETWORK,e,x),{cause:h.cause||h}):D.from(h,h&&h.code,e,x)}}),xo={http:Qg,xhr:jx,fetch:Px};j.forEach(xo,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Zu=e=>`- ${e}`,Lx=e=>j.isFunction(e)||e===null||e===!1,rm={getAdapter:e=>{e=j.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let l=0;l<t;l++){n=e[l];let o;if(r=n,!Lx(n)&&(r=xo[(o=String(n)).toLowerCase()],r===void 0))throw new D(`Unknown adapter '${o}'`);if(r)break;s[o||"#"+l]=r}if(!r){const l=Object.entries(s).map(([i,u])=>`adapter ${i} `+(u===!1?"is not supported by the environment":"is not available in the build"));let o=t?l.length>1?`since :
`+l.map(Zu).join(`
`):" "+Zu(l[0]):"as no adapter specified";throw new D("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:xo};function xa(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Wn(null,e)}function ec(e){return xa(e),e.headers=_e.from(e.headers),e.data=ga.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),rm.getAdapter(e.adapter||Jr.adapter)(e).then(function(r){return xa(e),r.data=ga.call(e,e.transformResponse,r),r.headers=_e.from(r.headers),r},function(r){return Yf(r)||(xa(e),r&&r.response&&(r.response.data=ga.call(e,e.transformResponse,r.response),r.response.headers=_e.from(r.response.headers))),Promise.reject(r)})}const sm="1.11.0",Al={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Al[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const tc={};Al.transitional=function(t,n,r){function s(l,o){return"[Axios v"+sm+"] Transitional option '"+l+"'"+o+(r?". "+r:"")}return(l,o,i)=>{if(t===!1)throw new D(s(o," has been removed"+(n?" in "+n:"")),D.ERR_DEPRECATED);return n&&!tc[o]&&(tc[o]=!0,console.warn(s(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(l,o,i):!0}};Al.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Ox(e,t,n){if(typeof e!="object")throw new D("options must be an object",D.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const l=r[s],o=t[l];if(o){const i=e[l],u=i===void 0||o(i,l,e);if(u!==!0)throw new D("option "+l+" must be "+u,D.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new D("Unknown option "+l,D.ERR_BAD_OPTION)}}const Ds={assertOptions:Ox,validators:Al},st=Ds.validators;let en=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Qu,response:new Qu}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const l=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?l&&!String(r.stack).endsWith(l.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+l):r.stack=l}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=an(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:l}=n;r!==void 0&&Ds.assertOptions(r,{silentJSONParsing:st.transitional(st.boolean),forcedJSONParsing:st.transitional(st.boolean),clarifyTimeoutError:st.transitional(st.boolean)},!1),s!=null&&(j.isFunction(s)?n.paramsSerializer={serialize:s}:Ds.assertOptions(s,{encode:st.function,serialize:st.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ds.assertOptions(n,{baseUrl:st.spelling("baseURL"),withXsrfToken:st.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=l&&j.merge(l.common,l[n.method]);l&&j.forEach(["delete","get","head","post","put","patch","common"],x=>{delete l[x]}),n.headers=_e.concat(o,l);const i=[];let u=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(u=u&&y.synchronous,i.unshift(y.fulfilled,y.rejected))});const c=[];this.interceptors.response.forEach(function(y){c.push(y.fulfilled,y.rejected)});let d,g=0,p;if(!u){const x=[ec.bind(this),void 0];for(x.unshift(...i),x.push(...c),p=x.length,d=Promise.resolve(n);g<p;)d=d.then(x[g++],x[g++]);return d}p=i.length;let w=n;for(g=0;g<p;){const x=i[g++],y=i[g++];try{w=x(w)}catch(N){y.call(this,N);break}}try{d=ec.call(this,w)}catch(x){return Promise.reject(x)}for(g=0,p=c.length;g<p;)d=d.then(c[g++],c[g++]);return d}getUri(t){t=an(this.defaults,t);const n=Zf(t.baseURL,t.url,t.allowAbsoluteUrls);return Kf(n,t.params,t.paramsSerializer)}};j.forEach(["delete","get","head","options"],function(t){en.prototype[t]=function(n,r){return this.request(an(r||{},{method:t,url:n,data:(r||{}).data}))}});j.forEach(["post","put","patch"],function(t){function n(r){return function(l,o,i){return this.request(an(i||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:l,data:o}))}}en.prototype[t]=n(),en.prototype[t+"Form"]=n(!0)});let Fx=class lm{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(l){n=l});const r=this;this.promise.then(s=>{if(!r._listeners)return;let l=r._listeners.length;for(;l-- >0;)r._listeners[l](s);r._listeners=null}),this.promise.then=s=>{let l;const o=new Promise(i=>{r.subscribe(i),l=i}).then(s);return o.cancel=function(){r.unsubscribe(l)},o},t(function(l,o,i){r.reason||(r.reason=new Wn(l,o,i),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new lm(function(s){t=s}),cancel:t}}};function Ax(e){return function(n){return e.apply(null,n)}}function Dx(e){return j.isObject(e)&&e.isAxiosError===!0}const yo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(yo).forEach(([e,t])=>{yo[t]=e});function am(e){const t=new en(e),n=Df(en.prototype.request,t);return j.extend(n,en.prototype,t,{allOwnKeys:!0}),j.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return am(an(e,s))},n}const ee=am(Jr);ee.Axios=en;ee.CanceledError=Wn;ee.CancelToken=Fx;ee.isCancel=Yf;ee.VERSION=sm;ee.toFormData=Ol;ee.AxiosError=D;ee.Cancel=ee.CanceledError;ee.all=function(t){return Promise.all(t)};ee.spread=Ax;ee.isAxiosError=Dx;ee.mergeConfig=an;ee.AxiosHeaders=_e;ee.formToJSON=e=>Xf(j.isHTMLForm(e)?new FormData(e):e);ee.getAdapter=rm.getAdapter;ee.HttpStatusCode=yo;ee.default=ee;const{Axios:yy,AxiosError:vy,CanceledError:wy,isCancel:Ny,CancelToken:jy,VERSION:ky,all:Sy,Cancel:Ey,isAxiosError:by,spread:Cy,toFormData:_y,AxiosHeaders:Ry,HttpStatusCode:Ty,formToJSON:Py,getAdapter:Ly,mergeConfig:Oy}=ee,om="http://localhost:5000/api",Mx={baseURL:om,timeout:1e4,headers:{"Content-Type":"application/json"}},Te=ee.create(Mx),Ne={getAccessToken:()=>localStorage.getItem("access_token"),setAccessToken:e=>{localStorage.setItem("access_token",e)},getRefreshToken:()=>localStorage.getItem("refresh_token"),setRefreshToken:e=>{localStorage.setItem("refresh_token",e)},removeRefreshToken:()=>{localStorage.removeItem("refresh_token")},clearTokens:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token")},getToken:()=>Ne.getAccessToken(),setToken:e=>{Ne.setAccessToken(e)},removeToken:()=>{localStorage.removeItem("access_token")}};Te.interceptors.request.use(e=>{const t=Ne.getAccessToken();return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));Te.interceptors.response.use(e=>e,async e=>{var n;const t=e.config;if(((n=e.response)==null?void 0:n.status)===401&&!t._retry){t._retry=!0;const r=Ne.getRefreshToken();if(r)try{const s=await ee.post(`${om}/auth/refresh`,{refresh_token:r}),{access_token:l}=s.data;return Ne.setAccessToken(l),t.headers.Authorization=`Bearer ${l}`,Te(t)}catch{Ne.clearTokens(),window.location.href="/login"}else Ne.clearTokens(),window.location.href="/login"}return Promise.reject(e)});const xs={async login(e){return(await Te.post("/auth/login",e)).data},async register(e){return(await Te.post("/auth/register",e)).data},async logout(){await Te.post("/auth/logout")},async getProfile(){return(await Te.get("/auth/profile")).data},async refreshToken(e){return(await Te.post("/auth/refresh-token",{refreshToken:e})).data}},im=v.createContext(void 0);function zx({children:e}){const[t,n]=v.useState(null),[r,s]=v.useState(!0),[l,o]=v.useState(null);v.useEffect(()=>{(async()=>{if(Ne.getAccessToken())try{const y=await xs.getProfile();n(y.data.user)}catch{Ne.clearTokens()}s(!1)})()},[]);const p={user:t,isLoading:r,error:l,login:async(w,x)=>{var y,N;try{o(null);const h=await xs.login({email:w,password:x}),{accessToken:m,refreshToken:f,user:k}=h.data;Ne.setAccessToken(m),Ne.setRefreshToken(f),n(k)}catch(h){const m=((N=(y=h.response)==null?void 0:y.data)==null?void 0:N.message)||h.message||"Login failed";throw o(m),new Error(m)}},register:async w=>{try{o(null);const x=await xs.register(w),{accessToken:y,refreshToken:N,user:h}=x.data;Ne.setAccessToken(y),Ne.setRefreshToken(N),n(h)}catch(x){const y=x instanceof Error?x.message:"Registration failed";throw o(y),x}},logout:async()=>{try{o(null),await xs.logout(),Ne.clearTokens(),n(null)}catch(w){const x=w instanceof Error?w.message:"Logout failed";throw o(x),w}},hasPermission:w=>t?t.role==="admin"?!0:({submit:["create_folder","create_form","perform_checksheet","submit_checksheet","save_final_approved_checksheet"],approval:["first_approval","create_folder","create_form","save_final_approved_checksheet"],final_approval:["final_approval","save_final_approved_checksheet","view_all_next_department_content","create_user"]}[t.role]||[]).includes(w):!1,clearError:()=>{o(null)}};return a.jsx(im.Provider,{value:p,children:e})}function nt(){const e=v.useContext(im);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e}/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ux={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ix=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),M=(e,t)=>{const n=v.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:i="",children:u,...c},d)=>v.createElement("svg",{ref:d,...Ux,width:s,height:s,stroke:r,strokeWidth:o?Number(l)*24/Number(s):l,className:["lucide",`lucide-${Ix(e)}`,i].join(" "),...c},[...t.map(([g,p])=>v.createElement(g,p)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ar=M("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const um=M("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=M("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $x=M("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nc=M("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=M("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bx=M("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dn=M("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hx=M("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dl=M("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cm=M("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=M("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=M("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=M("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=M("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vx=M("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ms=M("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wx=M("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qx=M("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vo=M("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qx=M("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kx=M("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ci=M("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jx=M("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xx=M("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yx=M("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gx=M("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mn=M("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dm=M("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=M("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zx=M("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ys=M("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vs=M("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _i=M("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rc=M("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=M("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wo=M("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ey=M("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mr=M("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ot=M("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sc=M("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);function ty(){const{user:e,login:t}=nt(),[n,r]=v.useState(""),[s,l]=v.useState(""),[o,i]=v.useState(""),[u,c]=v.useState(!1);if(e)return a.jsx(wi,{to:"/dashboard",replace:!0});const d=async g=>{g.preventDefault(),i(""),c(!0);try{await t(n,s)}catch(p){console.error("Login error:",p),i(p.message||"Login failed. Please check your credentials and try again.")}finally{c(!1)}};return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:a.jsx("div",{className:"max-w-md w-full space-y-8",children:a.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4",children:a.jsx(qx,{className:"h-6 w-6 text-white"})}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Welcome Back"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your FieldReporter account"})]}),o&&a.jsxs("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[a.jsx(Ar,{className:"h-5 w-5 text-red-500 flex-shrink-0"}),a.jsx("span",{className:"text-red-700 text-sm",children:o})]}),a.jsxs("form",{onSubmit:d,className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(Ci,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"email",type:"email",required:!0,value:n,onChange:g=>r(g.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(vo,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"password",type:"password",required:!0,value:s,onChange:g=>l(g.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your password"})]})]}),a.jsx("button",{type:"submit",disabled:u,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):"Sign In"})]}),a.jsxs("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[a.jsx("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Demo Credentials:"}),a.jsxs("div",{className:"text-xs text-gray-600 space-y-1",children:[a.jsxs("p",{children:[a.jsx("strong",{children:"Admin:"})," <EMAIL> / admin123"]}),a.jsxs("p",{children:[a.jsx("strong",{children:"Manager:"})," <EMAIL> / admin123"]}),a.jsxs("p",{children:[a.jsx("strong",{children:"User:"})," <EMAIL> / admin123"]})]})]}),a.jsx("div",{className:"mt-6 text-center",children:a.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",a.jsx("a",{href:"/register",className:"font-medium text-blue-600 hover:text-blue-500 transition-colors",children:"Sign up here"})]})})]})})})}function ny(){const{user:e,register:t}=nt(),[n,r]=v.useState({username:"",email:"",password:"",confirmPassword:""}),[s,l]=v.useState(""),[o,i]=v.useState(!1);if(e)return a.jsx(wi,{to:"/dashboard",replace:!0});const u=d=>{r({...n,[d.target.name]:d.target.value})},c=async d=>{if(d.preventDefault(),l(""),n.password!==n.confirmPassword){l("Passwords do not match");return}if(n.password.length<6){l("Password must be at least 6 characters long");return}i(!0);try{await t(n.username,n.email,n.password)}catch(g){l(g.message||"Registration failed")}finally{i(!1)}};return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:a.jsx("div",{className:"max-w-md w-full space-y-8",children:a.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[a.jsxs("div",{className:"text-center mb-8",children:[a.jsx("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4",children:a.jsx(ey,{className:"h-6 w-6 text-white"})}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Create Account"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Join FieldReporter today"})]}),s&&a.jsxs("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[a.jsx(Ar,{className:"h-5 w-5 text-red-500 flex-shrink-0"}),a.jsx("span",{className:"text-red-700 text-sm",children:s})]}),a.jsxs("form",{onSubmit:c,className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(Mr,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"username",name:"username",type:"text",required:!0,value:n.username,onChange:u,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your username"})]})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(Ci,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"email",name:"email",type:"email",required:!0,value:n.email,onChange:u,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(vo,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"password",name:"password",type:"password",required:!0,value:n.password,onChange:u,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your password"})]})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(vo,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:n.confirmPassword,onChange:u,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Confirm your password"})]})]}),a.jsx("button",{type:"submit",disabled:o,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:o?a.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):"Create Account"})]}),a.jsx("div",{className:"mt-6 text-center",children:a.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",a.jsx(ki,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500 transition-colors",children:"Sign in here"})]})})]})})})}function ry(){const{user:e,logout:t}=nt();return a.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"flex justify-between items-center h-16",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("div",{className:"flex-shrink-0",children:a.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"FieldReporter"})})}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full",children:a.jsx($x,{className:"h-5 w-5"})}),a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Mr,{className:"h-5 w-5 text-gray-400"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:(e==null?void 0:e.username)||(e==null?void 0:e.email)||"User"})]}),a.jsx("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full",title:"Logout",children:a.jsx(Kx,{className:"h-5 w-5"})})]})]})]})})})}function sy(){const{user:e}=nt(),t=[{name:"Dashboard",href:"/dashboard",icon:Wx},{name:"Departments",href:"/departments",icon:Dr},{name:"Forms",href:"/forms",icon:Le},{name:"Reports",href:"/reports",icon:hr},{name:"History",href:"/history",icon:Ms}];return((e==null?void 0:e.role)==="admin"||(e==null?void 0:e.role)==="final_approval")&&t.push({name:"User Management",href:"/admin/users",icon:ot}),a.jsx("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:top-16 bg-gray-900 text-white",children:a.jsxs("div",{className:"flex-1 flex flex-col min-h-0",children:[a.jsx("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:a.jsx("nav",{className:"mt-5 flex-1 px-2 space-y-1",children:t.map(n=>a.jsxs(Ff,{to:n.href,className:({isActive:r})=>`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${r?"bg-blue-500 text-white":"text-gray-300 hover:bg-blue-700 hover:text-white"}`,children:[a.jsx(n.icon,{className:"mr-3 flex-shrink-0 h-5 w-5","aria-hidden":"true"}),n.name]},n.name))})}),a.jsx("div",{className:"flex-shrink-0 flex bg-gray-800 p-4",children:a.jsx("div",{className:"flex items-center",children:a.jsxs("div",{className:"ml-3",children:[a.jsx("p",{className:"text-sm font-medium text-white",children:(e==null?void 0:e.username)||(e==null?void 0:e.email)}),a.jsx("p",{className:"text-xs font-medium text-gray-400 capitalize",children:e==null?void 0:e.role})]})})})]})})}function ly(){const{user:e}=nt(),t=[{title:"Total Forms",value:"24",change:"+12%",changeType:"positive",icon:Le,color:"bg-blue-500"},{title:"Pending Approvals",value:"8",change:"+3",changeType:"neutral",icon:dl,color:"bg-yellow-500"},{title:"Completed",value:"156",change:"+18%",changeType:"positive",icon:Dn,color:"bg-green-500"},{title:"Active Users",value:"42",change:"+5%",changeType:"positive",icon:ot,color:"bg-purple-500"}],n=[{id:1,action:"Form submitted",form:"Equipment Request Form",user:"John Doe",time:"2 hours ago",status:"pending"},{id:2,action:"Form approved",form:"Leave Request Form",user:"Jane Smith",time:"4 hours ago",status:"approved"},{id:3,action:"Form rejected",form:"Budget Request Form",user:"Mike Johnson",time:"6 hours ago",status:"rejected"},{id:4,action:"Form created",form:"Training Request Form",user:"Sarah Wilson",time:"1 day ago",status:"draft"}];return a.jsxs("div",{className:"space-y-6",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsxs("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",(e==null?void 0:e.username)||(e==null?void 0:e.email)||"User","!"]}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Here's what's happening with your forms today."})]}),a.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[a.jsx(Xr,{className:"w-4 h-4"}),a.jsx("span",{children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:t.map((r,s)=>a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:r.title}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-2",children:r.value}),a.jsxs("div",{className:"flex items-center mt-2",children:[a.jsx("span",{className:`text-sm font-medium ${r.changeType==="positive"?"text-green-600":r.changeType==="negative"?"text-red-600":"text-gray-600"}`,children:r.change}),a.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"from last month"})]})]}),a.jsx("div",{className:`${r.color} p-3 rounded-lg`,children:a.jsx(r.icon,{className:"w-6 h-6 text-white"})})]})},s))}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"}),a.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-700 font-medium",children:"View all"})]})}),a.jsx("div",{className:"p-6",children:a.jsx("div",{className:"space-y-4",children:n.map(r=>a.jsxs("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:`w-2 h-2 rounded-full mt-2 ${r.status==="approved"?"bg-green-500":r.status==="rejected"?"bg-red-500":r.status==="pending"?"bg-yellow-500":"bg-gray-400"}`}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsxs("p",{className:"text-sm text-gray-900",children:[a.jsx("span",{className:"font-medium",children:r.user})," ",r.action.toLowerCase()]}),a.jsx("p",{className:"text-sm text-gray-600",children:r.form}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:r.time})]}),a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${r.status==="approved"?"bg-green-100 text-green-800":r.status==="rejected"?"bg-red-100 text-red-800":r.status==="pending"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:r.status})]},r.id))})})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Quick Actions"})}),a.jsx("div",{className:"p-6",children:a.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[a.jsxs("button",{className:"flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors",children:[a.jsx(Le,{className:"w-8 h-8 text-gray-400 mb-2"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Create Form"})]}),a.jsxs("button",{className:"flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors",children:[a.jsx(Dn,{className:"w-8 h-8 text-gray-400 mb-2"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Review Pending"})]}),a.jsxs("button",{className:"flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors",children:[a.jsx(hr,{className:"w-8 h-8 text-gray-400 mb-2"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"View Reports"})]}),a.jsxs("button",{className:"flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors",children:[a.jsx(ot,{className:"w-8 h-8 text-gray-400 mb-2"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Manage Users"})]})]})})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Form Submissions Overview"}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md",children:"7 days"}),a.jsx("button",{className:"px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md",children:"30 days"}),a.jsx("button",{className:"px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md",children:"90 days"})]})]}),a.jsx("div",{className:"h-64 bg-gray-50 rounded-lg flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx(wo,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"Chart visualization would go here"}),a.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Integration with charting library needed"})]})})]})]})}const lc={async getForms(e){return(await Te.get("/forms",{params:e})).data},async getFormById(e){return(await Te.get(`/forms/${e}`)).data},async createForm(e){return(await Te.post("/forms",e)).data},async updateForm(e,t){return(await Te.put(`/forms/${e}`,t)).data},async deleteForm(e){return(await Te.delete(`/forms/${e}`)).data}};function ay(){const{user:e}=nt(),t=Vr(),[n,r]=v.useState(""),[s,l]=v.useState("all"),[o,i]=v.useState("all"),[u,c]=v.useState([]),[d,g]=v.useState(!0),[p,w]=v.useState(null),[x,y]=v.useState(1),[N,h]=v.useState(1);v.useEffect(()=>{let S=!0;return(async()=>{try{g(!0),w(null);const O=await lc.getForms({page:x,limit:10,search:n||void 0,folder_id:s==="all"?void 0:s});S&&(c(O.data.forms),h(O.data.pagination.pages))}catch(O){S&&(w("Failed to fetch forms. Please try again later."),console.error("Error fetching forms:",O))}finally{S&&g(!1)}})(),()=>{S=!1}},[x,n,s]);const m=()=>{t("/forms/create")},f=S=>{t(`/forms/edit/${S}`)},k=async S=>{if(window.confirm("Are you sure you want to delete this form?"))try{await lc.deleteForm(S),c(u.filter(R=>R.id!==S))}catch(R){console.error("Error deleting form:",R),alert("Failed to delete form. Please try again later.")}},E=[{id:"all",name:"All Forms"},{id:"safety",name:"Safety Forms"},{id:"maintenance",name:"Maintenance"},{id:"reports",name:"Reports"}],C=u.filter(S=>o==="all"||S.status===o);return d?a.jsx("div",{className:"flex items-center justify-center h-screen",children:a.jsx(Qx,{className:"w-8 h-8 animate-spin text-blue-600"})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Forms"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage and organize your forms"})]}),a.jsxs("button",{onClick:m,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[a.jsx(Mn,{className:"w-4 h-4"}),a.jsx("span",{children:"Create Form"})]})]}),p&&a.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 rounded-lg p-4",children:p}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:a.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4",children:[a.jsxs("div",{className:"relative flex-1 max-w-md",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(zl,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"Search forms...",value:n,onChange:S=>r(S.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Dr,{className:"w-4 h-4 text-gray-500"}),a.jsx("select",{value:s,onChange:S=>l(S.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:E.map(S=>a.jsx("option",{value:S.id,children:S.name},S.id))})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Ml,{className:"w-4 h-4 text-gray-500"}),a.jsxs("select",{value:o,onChange:S=>i(S.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"active",children:"Active"}),a.jsx("option",{value:"draft",children:"Draft"}),a.jsx("option",{value:"archived",children:"Archived"})]})]})]})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:C.map(S=>a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow",children:a.jsxs("div",{className:"p-6",children:[a.jsx("div",{className:"flex items-start justify-between mb-4",children:a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:`p-2 rounded-lg ${S.type==="excel"?"bg-green-100":"bg-blue-100"}`,children:a.jsx(Le,{className:`w-5 h-5 ${S.type==="excel"?"text-green-600":"text-blue-600"}`})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-semibold text-gray-900",children:S.name}),a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${S.status==="active"?"bg-green-100 text-green-800":S.status==="draft"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:S.status})]})]})}),a.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:S.description}),a.jsxs("div",{className:"space-y-2 mb-4",children:[a.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[a.jsx(Dr,{className:"w-4 h-4 mr-2"}),a.jsx("span",{children:S.folder})]}),a.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[a.jsx(Mr,{className:"w-4 h-4 mr-2"}),a.jsx("span",{children:S.created_by})]}),a.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[a.jsx(Xr,{className:"w-4 h-4 mr-2"}),a.jsx("span",{children:S.created_at})]})]}),a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsxs("span",{className:"text-sm text-gray-600",children:[S.submissions," submissions"]}),a.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${S.type==="excel"?"bg-green-50 text-green-700":"bg-blue-50 text-blue-700"}`,children:S.type.toUpperCase()})]}),a.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",onClick:()=>t(`/forms/${S.id}`),title:"View Form",children:a.jsx(Dl,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>t(`/checksheet/${S.id}`),className:"p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Perform Checksheet",children:a.jsx(Gx,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>f(S.id),className:"p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors",title:"Edit Form",children:a.jsx(_i,{className:"w-4 h-4"})}),a.jsx("button",{onClick:()=>k(S.id),className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"Delete Form",children:a.jsx(Ul,{className:"w-4 h-4"})})]}),a.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-700 font-medium",children:"View Details"})]})]})},S.id))}),C.length===0&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[a.jsx(Le,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No forms found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:n||s!=="all"||o!=="all"?"Try adjusting your search or filters":"Get started by creating your first form"}),a.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors",children:[a.jsx(Mn,{className:"w-4 h-4"}),a.jsx("span",{children:"Create Form"})]})]})]})}function oy(){var i;const[e,t]=v.useState("submissions"),[n,r]=v.useState("last30days"),s=[{id:"submissions",name:"Form Submissions",icon:Le},{id:"users",name:"User Activity",icon:ot},{id:"performance",name:"Performance Metrics",icon:wo},{id:"departments",name:"Department Analytics",icon:hr}],l={submissions:{total:1247,thisMonth:89,change:"+12%",data:[{name:"Employee Onboarding",submissions:45,completion:"92%"},{name:"Leave Request",submissions:32,completion:"98%"},{name:"Expense Report",submissions:28,completion:"85%"},{name:"Performance Review",submissions:15,completion:"76%"}]},users:{total:156,active:134,change:"+5%",data:[{name:"HR Department",users:12,active:11},{name:"Engineering",users:45,active:42},{name:"Sales",users:23,active:21},{name:"Marketing",users:18,active:16}]}},o=l[e]||l.submissions;return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Reports & Analytics"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"View detailed insights and generate reports"})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs("button",{className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:[a.jsx(Ml,{className:"w-4 h-4 mr-2"}),"Filters"]}),a.jsxs("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(cm,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:s.map(u=>{const c=u.icon;return a.jsxs("button",{onClick:()=>t(u.id),className:`p-4 rounded-lg border-2 transition-all ${e===u.id?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300 text-gray-700"}`,children:[a.jsx(c,{className:"w-6 h-6 mx-auto mb-2"}),a.jsx("p",{className:"font-medium text-sm",children:u.name})]},u.id)})}),a.jsxs("div",{className:"flex items-center space-x-4 bg-white p-4 rounded-lg border border-gray-200",children:[a.jsx(Xr,{className:"w-5 h-5 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Date Range:"}),a.jsxs("select",{value:n,onChange:u=>r(u.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"last7days",children:"Last 7 days"}),a.jsx("option",{value:"last30days",children:"Last 30 days"}),a.jsx("option",{value:"last90days",children:"Last 90 days"}),a.jsx("option",{value:"lastyear",children:"Last year"}),a.jsx("option",{value:"custom",children:"Custom range"})]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[a.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsxs("p",{className:"text-sm font-medium text-gray-600",children:["Total ",e==="submissions"?"Submissions":"Users"]}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:o.total})]}),a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(Le,{className:"w-6 h-6 text-blue-600"})})]}),a.jsxs("div",{className:"mt-4 flex items-center",children:[a.jsx("span",{className:"text-green-600 text-sm font-medium",children:o.change}),a.jsx("span",{className:"text-gray-600 text-sm ml-2",children:"from last period"})]})]}),a.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"This Month"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:e==="submissions"?o.thisMonth:o.active})]}),a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(wo,{className:"w-6 h-6 text-green-600"})})]}),a.jsxs("div",{className:"mt-4 flex items-center",children:[a.jsx("span",{className:"text-blue-600 text-sm font-medium",children:"+8.2%"}),a.jsx("span",{className:"text-gray-600 text-sm ml-2",children:"from last month"})]})]}),a.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Completion Rate"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"89.5%"})]}),a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(hr,{className:"w-6 h-6 text-purple-600"})})]}),a.jsxs("div",{className:"mt-4 flex items-center",children:[a.jsx("span",{className:"text-green-600 text-sm font-medium",children:"+2.1%"}),a.jsx("span",{className:"text-gray-600 text-sm ml-2",children:"improvement"})]})]})]}),a.jsxs("div",{className:"bg-white rounded-lg border border-gray-200",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[(i=s.find(u=>u.id===e))==null?void 0:i.name," Details"]})}),a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e==="submissions"?"Submissions":"Users"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e==="submissions"?"Completion Rate":"Active"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:o.data.map((u,c)=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("div",{className:"text-sm font-medium text-gray-900",children:u.name})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("div",{className:"text-sm text-gray-900",children:e==="submissions"?u.submissions:u.users})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("div",{className:"text-sm text-gray-900",children:e==="submissions"?u.completion:u.active})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:a.jsxs("button",{className:"text-blue-600 hover:text-blue-900 flex items-center",children:[a.jsx(Dl,{className:"w-4 h-4 mr-1"}),"View Details"]})})]},c))})]})})]}),a.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Trends Over Time"}),a.jsx("div",{className:"h-64 bg-gray-50 rounded-lg flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx(hr,{className:"w-12 h-12 text-gray-400 mx-auto mb-2"}),a.jsx("p",{className:"text-gray-500",children:"Chart visualization will be displayed here"}),a.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Integration with charting library needed"})]})})]})]})}function iy(){const e=Vr(),[t,n]=v.useState("survey"),[r,s]=v.useState({name:"",description:"",folder_id:"",category:"general"}),[l,o]=v.useState([{id:"1",type:"text",title:"Sample Text Field",required:!1,placeholder:"Enter text here..."}]),i=[{value:"text",label:"Text Input",icon:Le},{value:"textarea",label:"Text Area",icon:Le},{value:"radio",label:"Radio Buttons",icon:ys},{value:"checkbox",label:"Checkboxes",icon:ys},{value:"dropdown",label:"Dropdown",icon:ys},{value:"rating",label:"Rating Scale",icon:ys}],u=x=>{s({...r,[x.target.name]:x.target.value})},c=x=>{const y={id:Date.now().toString(),type:x,title:`New ${x} field`,required:!1,placeholder:x==="text"?"Enter text...":""};o([...l,y])},d=x=>{o(l.filter(y=>y.id!==x))},g=(x,y,N)=>{o(l.map(h=>h.id===x?{...h,[y]:N}:h))},p=()=>{console.log("Saving form:",{formData:r,formType:t,surveyElements:l}),e("/forms")},w=()=>{console.log("Previewing form:",{formData:r,formType:t,surveyElements:l})};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>e("/forms"),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(um,{className:"w-5 h-5"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Form"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Build your custom form with drag-and-drop elements"})]})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs("button",{onClick:w,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:[a.jsx(Dl,{className:"w-4 h-4 mr-2"}),"Preview"]}),a.jsxs("button",{onClick:p,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(dm,{className:"w-4 h-4 mr-2"}),"Save Form"]})]})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[a.jsxs("div",{className:"lg:col-span-1 space-y-6",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Form Settings"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Form Name"}),a.jsx("input",{type:"text",name:"name",value:r.name,onChange:u,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter form name"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),a.jsx("textarea",{name:"description",value:r.description,onChange:u,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Describe your form"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Form Type"}),a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[a.jsxs("button",{onClick:()=>n("survey"),className:`p-3 border-2 rounded-lg text-center transition-colors ${t==="survey"?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-gray-300"}`,children:[a.jsx(Le,{className:"w-5 h-5 mx-auto mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Survey"})]}),a.jsxs("button",{onClick:()=>n("excel"),className:`p-3 border-2 rounded-lg text-center transition-colors ${t==="excel"?"border-green-500 bg-green-50 text-green-700":"border-gray-200 hover:border-gray-300"}`,children:[a.jsx(rc,{className:"w-5 h-5 mx-auto mb-1"}),a.jsx("span",{className:"text-xs font-medium",children:"Excel"})]})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),a.jsxs("select",{name:"category",value:r.category,onChange:u,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[a.jsx("option",{value:"general",children:"General"}),a.jsx("option",{value:"safety",children:"Safety"}),a.jsx("option",{value:"maintenance",children:"Maintenance"}),a.jsx("option",{value:"quality",children:"Quality Control"}),a.jsx("option",{value:"hr",children:"Human Resources"})]})]})]})]}),t==="survey"&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Add Elements"}),a.jsx("div",{className:"space-y-2",children:i.map(x=>{const y=x.icon;return a.jsxs("button",{onClick:()=>c(x.value),className:"w-full flex items-center p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors",children:[a.jsx(y,{className:"w-4 h-4 mr-3 text-gray-500"}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:x.label}),a.jsx(Mn,{className:"w-4 h-4 ml-auto text-gray-400"})]},x.value)})})]})]}),a.jsx("div",{className:"lg:col-span-3",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[a.jsx("div",{className:"p-6 border-b border-gray-200",children:a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:t==="survey"?"Form Builder":"Excel Template Designer"})}),a.jsx("div",{className:"p-6",children:t==="survey"?a.jsxs("div",{className:"space-y-4",children:[l.map((x,y)=>a.jsxs("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors",children:[a.jsxs("div",{className:"flex items-center justify-between mb-3",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Vx,{className:"w-4 h-4 text-gray-400 cursor-move"}),a.jsxs("span",{className:"text-sm font-medium text-gray-700 capitalize",children:[x.type," Field"]})]}),a.jsx("button",{onClick:()=>d(x.id),className:"p-1 text-gray-400 hover:text-red-600 transition-colors",children:a.jsx(Ul,{className:"w-4 h-4"})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Field Title"}),a.jsx("input",{type:"text",value:x.title,onChange:N=>g(x.id,"title",N.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),x.type==="text"&&a.jsxs("div",{children:[a.jsx("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Placeholder"}),a.jsx("input",{type:"text",value:x.placeholder,onChange:N=>g(x.id,"placeholder",N.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:`required-${x.id}`,checked:x.required,onChange:N=>g(x.id,"required",N.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:`required-${x.id}`,className:"ml-2 text-xs text-gray-600",children:"Required field"})]}),a.jsxs("div",{className:"pt-2 border-t border-gray-100",children:[a.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[x.title,x.required&&a.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),x.type==="text"&&a.jsx("input",{type:"text",placeholder:x.placeholder,className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50",disabled:!0}),x.type==="textarea"&&a.jsx("textarea",{rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50",disabled:!0})]})]})]},x.id)),l.length===0&&a.jsxs("div",{className:"text-center py-12 text-gray-500",children:[a.jsx(Le,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{className:"text-lg font-medium mb-2",children:"Start building your form"}),a.jsx("p",{className:"text-sm",children:"Add elements from the sidebar to get started"})]})]}):a.jsxs("div",{className:"text-center py-12 text-gray-500",children:[a.jsx(rc,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),a.jsx("p",{className:"text-lg font-medium mb-2",children:"Excel Template Designer"}),a.jsx("p",{className:"text-sm",children:"Excel form builder coming soon"})]})})]})})]})]})}function uy(){const{user:e}=nt(),[t,n]=v.useState(""),[r,s]=v.useState(null),l=[{id:"1",name:"Tổng Công Ty",type:"general",parent_id:null,users_count:156,forms_count:24,children:[{id:"2",name:"Phòng IT",type:"next",parent_id:"1",users_count:45,forms_count:12,children:[{id:"3",name:"Team Development",type:"direct",parent_id:"2",users_count:25,forms_count:8},{id:"4",name:"Team Infrastructure",type:"direct",parent_id:"2",users_count:20,forms_count:4}]},{id:"5",name:"Phòng Sản Xuất",type:"next",parent_id:"1",users_count:78,forms_count:18,children:[{id:"6",name:"Team QC",type:"direct",parent_id:"5",users_count:35,forms_count:10},{id:"7",name:"Team Production",type:"direct",parent_id:"5",users_count:43,forms_count:8}]}]}],o=(i,u=0)=>{const c=i.children&&i.children.length>0,d=r===i.id;return a.jsxs("div",{className:"mb-2",children:[a.jsxs("div",{className:`flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors cursor-pointer ${u>0?"ml-6":""}`,onClick:()=>s(d?null:i.id),children:[a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:`p-2 rounded-lg ${i.type==="general"?"bg-blue-100":i.type==="next"?"bg-green-100":"bg-purple-100"}`,children:a.jsx(nc,{className:`w-5 h-5 ${i.type==="general"?"text-blue-600":i.type==="next"?"text-green-600":"text-purple-600"}`})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-semibold text-gray-900",children:i.name}),a.jsxs("p",{className:"text-sm text-gray-600 capitalize",children:[i.type," Department"]})]})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[a.jsxs("div",{className:"flex items-center space-x-1",children:[a.jsx(ot,{className:"w-4 h-4"}),a.jsxs("span",{children:[i.users_count," users"]})]}),a.jsxs("div",{className:"flex items-center space-x-1",children:[a.jsx(Dr,{className:"w-4 h-4"}),a.jsxs("span",{children:[i.forms_count," forms"]})]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{className:"p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:a.jsx(_i,{className:"w-4 h-4"})}),a.jsx("button",{className:"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:a.jsx(Ul,{className:"w-4 h-4"})}),c&&a.jsx(Hx,{className:`w-4 h-4 text-gray-400 transition-transform ${d?"rotate-90":""}`})]})]})]}),c&&d&&a.jsx("div",{className:"mt-2",children:i.children.map(g=>o(g,u+1))})]},i.id)};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Departments"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage organizational structure and hierarchy"})]}),((e==null?void 0:e.role)==="admin"||(e==null?void 0:e.role)==="final_approval")&&a.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[a.jsx(Mn,{className:"w-4 h-4"}),a.jsx("span",{children:"Add Department"})]})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:a.jsxs("div",{className:"relative max-w-md",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(zl,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"Search departments...",value:t,onChange:i=>n(i.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})}),a.jsx("div",{className:"space-y-4",children:l.map(i=>o(i))}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Departments"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"7"})]}),a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(nc,{className:"w-6 h-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"156"})]}),a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(ot,{className:"w-6 h-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Forms"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"42"})]}),a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(Dr,{className:"w-6 h-6 text-purple-600"})})]})})]})]})}function cy(){const{user:e}=nt(),[t,n]=v.useState(""),[r,s]=v.useState("all"),[l,o]=v.useState("all"),i=[{id:"1",username:"admin",email:"<EMAIL>",role:"admin",general_department:"Tổng Công Ty",next_department:"Phòng IT",direct_department:"Team Development",is_active:!0,created_at:"2024-01-01",last_login:"2024-01-20"},{id:"2",username:"manager_it",email:"<EMAIL>",role:"final_approval",general_department:"Tổng Công Ty",next_department:"Phòng IT",direct_department:"Team Development",is_active:!0,created_at:"2024-01-02",last_login:"2024-01-19"},{id:"3",username:"approval_it",email:"<EMAIL>",role:"approval",general_department:"Tổng Công Ty",next_department:"Phòng IT",direct_department:"Team Development",is_active:!0,created_at:"2024-01-03",last_login:"2024-01-18"},{id:"4",username:"user_dev1",email:"<EMAIL>",role:"submit",general_department:"Tổng Công Ty",next_department:"Phòng IT",direct_department:"Team Development",is_active:!0,created_at:"2024-01-04",last_login:"2024-01-17"}],u=[{value:"all",label:"All Roles"},{value:"admin",label:"Admin"},{value:"final_approval",label:"Final Approval"},{value:"approval",label:"Approval"},{value:"submit",label:"Submit"}],c=[{value:"all",label:"All Departments"},{value:"it",label:"Phòng IT"},{value:"production",label:"Phòng Sản Xuất"}],d=p=>{switch(p){case"admin":return"bg-red-100 text-red-800";case"final_approval":return"bg-purple-100 text-purple-800";case"approval":return"bg-blue-100 text-blue-800";case"submit":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},g=i.filter(p=>{const w=p.username.toLowerCase().includes(t.toLowerCase())||p.email.toLowerCase().includes(t.toLowerCase()),x=r==="all"||p.role===r,y=l==="all"||p.next_department.toLowerCase().includes(l);return w&&x&&y});return(e==null?void 0:e.role)!=="admin"&&(e==null?void 0:e.role)!=="final_approval"?a.jsx("div",{className:"flex items-center justify-center min-h-96",children:a.jsxs("div",{className:"text-center",children:[a.jsx(vs,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),a.jsx("p",{className:"text-gray-600",children:"You don't have permission to access user management."})]})}):a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Manage users and their permissions"})]}),a.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[a.jsx(Mn,{className:"w-4 h-4"}),a.jsx("span",{children:"Add User"})]})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:a.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4",children:[a.jsxs("div",{className:"relative flex-1 max-w-md",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(zl,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"Search users...",value:t,onChange:p=>n(p.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(vs,{className:"w-4 h-4 text-gray-500"}),a.jsx("select",{value:r,onChange:p=>s(p.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:u.map(p=>a.jsx("option",{value:p.value,children:p.label},p.value))})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Ml,{className:"w-4 h-4 text-gray-500"}),a.jsx("select",{value:l,onChange:p=>o(p.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:c.map(p=>a.jsx("option",{value:p.value,children:p.label},p.value))})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Department"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Login"}),a.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(p=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:a.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:a.jsx(ot,{className:"h-5 w-5 text-gray-600"})})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:p.username}),a.jsxs("div",{className:"text-sm text-gray-500 flex items-center",children:[a.jsx(Ci,{className:"w-3 h-3 mr-1"}),p.email]})]})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${d(p.role)}`,children:p.role.replace("_"," ")})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.jsxs("div",{children:[a.jsx("div",{className:"font-medium",children:p.next_department}),a.jsx("div",{className:"text-gray-500",children:p.direct_department})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${p.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:p.is_active?"Active":"Inactive"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(Xr,{className:"w-3 h-3 mr-1"}),p.last_login]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:a.jsxs("div",{className:"flex items-center justify-end space-x-2",children:[a.jsx("button",{className:"text-blue-600 hover:text-blue-900",children:a.jsx(_i,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-red-600 hover:text-red-900",children:a.jsx(Ul,{className:"w-4 h-4"})}),a.jsx("button",{className:"text-gray-400 hover:text-gray-600",children:a.jsx(Xx,{className:"w-4 h-4"})})]})})]},p.id))})]})})}),g.length===0&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[a.jsx(ot,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:t||r!=="all"||l!=="all"?"Try adjusting your search or filters":"Get started by adding your first user"}),a.jsxs("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors",children:[a.jsx(Mn,{className:"w-4 h-4"}),a.jsx("span",{children:"Add User"})]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Users"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.length})]}),a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(ot,{className:"w-6 h-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Users"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.filter(p=>p.is_active).length})]}),a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(vs,{className:"w-6 h-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Admins"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.filter(p=>p.role==="admin").length})]}),a.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:a.jsx(vs,{className:"w-6 h-6 text-red-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Regular Users"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.filter(p=>p.role==="submit").length})]}),a.jsx("div",{className:"p-3 bg-purple-100 rounded-full",children:a.jsx(ot,{className:"w-6 h-6 text-purple-600"})})]})})]})]})}function dy(){const{formId:e}=h0(),t=Vr(),{user:n}=nt(),[r,s]=v.useState(null),[l,o]=v.useState([]),[i,u]=v.useState({}),[c,d]=v.useState(0),[g,p]=v.useState(!0),[w,x]=v.useState(!1),[y,N]=v.useState(!0),[h,m]=v.useState(null);v.useEffect(()=>{(async()=>{p(!0);const L={id:"session-1",form_id:e||"form-1",form_name:"Daily Safety Inspection",session_name:`Safety Check - ${new Date().toLocaleDateString()}`,status:"in_progress",progress_percentage:0,total_questions:6,answered_questions:0,started_at:new Date().toISOString(),location_name:"Building A - Floor 1"},B=[{id:"inspector_name",type:"text",title:"Inspector Name",isRequired:!0,placeholder:"Enter your name"},{id:"location",type:"text",title:"Inspection Location",isRequired:!0,placeholder:"Enter location details"},{id:"fire_safety",type:"radio",title:"Are fire safety equipment accessible and in good condition?",isRequired:!0,choices:["Yes","No","N/A"]},{id:"emergency_exits",type:"radio",title:"Are emergency exits clear and properly marked?",isRequired:!0,choices:["Yes","No"]},{id:"overall_rating",type:"rating",title:"Overall Safety Rating",isRequired:!0,rateMin:1,rateMax:5},{id:"notes",type:"textarea",title:"Additional Notes and Observations",isRequired:!1,rows:4,placeholder:"Enter any additional observations or concerns..."}];s(L),o(B);const we={};B.forEach(Ht=>{we[Ht.id]={question_id:Ht.id,response_value:"",is_answered:!1}}),u(we),p(!1)})()},[e]),v.useEffect(()=>{y&&navigator.geolocation&&navigator.geolocation.getCurrentPosition(P=>{m({lat:P.coords.latitude,lng:P.coords.longitude,name:`Location: ${P.coords.latitude.toFixed(6)}, ${P.coords.longitude.toFixed(6)}`}),N(!1)},P=>{console.error("Error getting location:",P),N(!1)})},[y]);const f=(P,L)=>{if(u(B=>({...B,[P]:{...B[P],response_value:L,is_answered:L.trim()!==""}})),r){const B=Object.values({...i,[P]:{...i[P],is_answered:L.trim()!==""}}).filter(we=>we.is_answered).length;s(we=>we?{...we,answered_questions:B,progress_percentage:B/we.total_questions*100}:null)}},k=async()=>{x(!0),await new Promise(P=>setTimeout(P,500)),x(!1)},E=()=>{c<l.length-1&&d(P=>P+1)},C=()=>{c>0&&d(P=>P-1)},S=async()=>{x(!0);const P=l.filter(L=>{var B;return L.isRequired&&!((B=i[L.id])!=null&&B.is_answered)});if(P.length>0){alert(`Please answer all required questions: ${P.map(L=>L.title).join(", ")}`),x(!1);return}await new Promise(L=>setTimeout(L,1e3)),x(!1),t("/forms",{state:{message:"Checksheet completed successfully!"}})},R=P=>{var B,we,Ht;const L=i[P.id];switch(P.type){case"text":return a.jsx("input",{type:"text",value:(L==null?void 0:L.response_value)||"",onChange:V=>f(P.id,V.target.value),placeholder:P.placeholder,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"});case"textarea":return a.jsx("textarea",{value:(L==null?void 0:L.response_value)||"",onChange:V=>f(P.id,V.target.value),placeholder:P.placeholder,rows:P.rows||4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"});case"radio":return a.jsx("div",{className:"space-y-3",children:(B=P.choices)==null?void 0:B.map((V,Ve)=>a.jsxs("label",{className:"flex items-center space-x-3 cursor-pointer",children:[a.jsx("input",{type:"radio",name:P.id,value:V,checked:(L==null?void 0:L.response_value)===V,onChange:_=>f(P.id,_.target.value),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),a.jsx("span",{className:"text-gray-700",children:V})]},Ve))});case"checkbox":return a.jsx("div",{className:"space-y-3",children:(we=P.choices)==null?void 0:we.map((V,Ve)=>a.jsxs("label",{className:"flex items-center space-x-3 cursor-pointer",children:[a.jsx("input",{type:"checkbox",value:V,checked:L==null?void 0:L.response_value.includes(V),onChange:_=>{const A=L!=null&&L.response_value?L.response_value.split(","):[],z=_.target.checked?[...A,V]:A.filter(J=>J!==V);f(P.id,z.join(","))},className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("span",{className:"text-gray-700",children:V})]},Ve))});case"dropdown":return a.jsxs("select",{value:(L==null?void 0:L.response_value)||"",onChange:V=>f(P.id,V.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[a.jsx("option",{value:"",children:"Select an option..."}),(Ht=P.choices)==null?void 0:Ht.map((V,Ve)=>a.jsx("option",{value:V,children:V},Ve))]});case"rating":return a.jsxs("div",{className:"flex items-center space-x-2",children:[Array.from({length:(P.rateMax||5)-(P.rateMin||1)+1},(V,Ve)=>{const _=(P.rateMin||1)+Ve;return a.jsx("button",{onClick:()=>f(P.id,_.toString()),className:`w-10 h-10 rounded-full border-2 font-medium transition-colors ${(L==null?void 0:L.response_value)===_.toString()?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:border-blue-400"}`,children:_},_)}),a.jsxs("span",{className:"text-sm text-gray-500 ml-4",children:[P.rateMin||1," = Poor, ",P.rateMax||5," = Excellent"]})]});case"date":return a.jsx("input",{type:"date",value:(L==null?void 0:L.response_value)||"",onChange:V=>f(P.id,V.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"});default:return null}};if(g)return a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading checksheet..."})]})});if(!r||l.length===0)return a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx(Ar,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Checksheet not found"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"The requested checksheet could not be loaded."}),a.jsx("button",{onClick:()=>t("/forms"),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Back to Forms"})]})});const O=l[c],F=i[O.id];return a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white shadow-sm border-b border-gray-200",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"flex items-center justify-between h-16",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>t("/forms"),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(um,{className:"w-5 h-5"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-lg font-semibold text-gray-900",children:r.form_name}),a.jsx("p",{className:"text-sm text-gray-600",children:r.session_name})]})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[h&&a.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx(Jx,{className:"w-4 h-4 mr-1"}),a.jsx("span",{children:"Location captured"})]}),a.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx(dl,{className:"w-4 h-4 mr-1"}),a.jsxs("span",{children:["Started ",new Date(r.started_at).toLocaleTimeString()]})]})]})]})})}),a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("span",{className:"text-sm font-medium text-gray-700",children:["Question ",c+1," of ",l.length]}),a.jsxs("span",{className:"text-sm text-gray-600",children:[Math.round(r.progress_percentage),"% Complete"]})]}),a.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${r.progress_percentage}%`}})})]})}),a.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[a.jsxs("div",{className:"mb-8",children:[a.jsxs("div",{className:"flex items-start space-x-3 mb-6",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:a.jsx(Le,{className:"w-4 h-4 text-blue-600"})})}),a.jsxs("div",{className:"flex-1",children:[a.jsxs("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:[O.title,O.isRequired&&a.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),O.isRequired&&a.jsx("p",{className:"text-sm text-gray-600",children:"This question is required"})]}),(F==null?void 0:F.is_answered)&&a.jsx(Dn,{className:"w-6 h-6 text-green-500"})]}),a.jsx("div",{className:"mb-6",children:R(O)}),a.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[a.jsxs("button",{className:"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[a.jsx(Bx,{className:"w-4 h-4"}),a.jsx("span",{children:"Add Photo"})]}),a.jsxs("button",{className:"flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[a.jsx(Yx,{className:"w-4 h-4"}),a.jsx("span",{children:"Attach File"})]})]})]}),a.jsxs("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[a.jsxs("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:C,disabled:c===0,className:"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"Previous"}),a.jsxs("button",{onClick:k,disabled:w,className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:[w?a.jsx("div",{className:"w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2"}):a.jsx(dm,{className:"w-4 h-4 mr-2"}),"Save"]})]}),a.jsx("div",{className:"flex space-x-3",children:c<l.length-1?a.jsx("button",{onClick:E,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Next"}):a.jsxs("button",{onClick:S,disabled:w,className:"flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:[w?a.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}):a.jsx(Zx,{className:"w-4 h-4 mr-2"}),"Complete Checksheet"]})})]})]}),a.jsxs("div",{className:"mt-6 bg-white rounded-lg shadow-sm p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Question Overview"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:l.map((P,L)=>{const B=i[P.id];return a.jsxs("button",{onClick:()=>d(L),className:`p-3 text-left border rounded-lg transition-colors ${L===c?"border-blue-500 bg-blue-50":B!=null&&B.is_answered?"border-green-300 bg-green-50":"border-gray-300 hover:border-gray-400"}`,children:[a.jsxs("div",{className:"flex items-center justify-between mb-1",children:[a.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["Q",L+1]}),(B==null?void 0:B.is_answered)&&a.jsx(Dn,{className:"w-4 h-4 text-green-500"})]}),a.jsx("p",{className:"text-sm text-gray-600 truncate",children:P.title})]},P.id)})})]})]})]})}function fy(){const{user:e}=nt(),[t,n]=v.useState(""),[r,s]=v.useState("all"),[l,o]=v.useState("all"),[i,u]=v.useState("all"),[c,d]=v.useState("today"),g=[{id:"1",timestamp:"2024-01-20 14:30:25",user:"admin",action:"LOGIN",target:"System",targetType:"system",status:"success",details:"User logged in successfully",ipAddress:"*************",userAgent:"Chrome/120.0.0.0"},{id:"2",timestamp:"2024-01-20 14:25:15",user:"<EMAIL>",action:"CREATE_FORM",target:"Daily Safety Check",targetType:"form",status:"success",details:"Created new safety inspection form",ipAddress:"*************"},{id:"3",timestamp:"2024-01-20 14:20:10",user:"<EMAIL>",action:"SUBMIT_FORM",target:"Quality Control Inspection",targetType:"submission",status:"success",details:"Submitted quality control form for batch QC-2024-001",ipAddress:"*************"},{id:"4",timestamp:"2024-01-20 14:15:05",user:"<EMAIL>",action:"APPROVE_SUBMISSION",target:"Equipment Maintenance Log",targetType:"submission",status:"success",details:"Approved maintenance log submission",ipAddress:"*************"},{id:"5",timestamp:"2024-01-20 14:10:30",user:"<EMAIL>",action:"UPDATE_FORM",target:"Server Maintenance Form",targetType:"form",status:"success",details:"Updated form fields and validation rules",ipAddress:"*************"},{id:"6",timestamp:"2024-01-20 14:05:20",user:"<EMAIL>",action:"CREATE_USER",target:"<EMAIL>",targetType:"user",status:"success",details:"Created new user account with submit role",ipAddress:"*************"},{id:"7",timestamp:"2024-01-20 14:00:15",user:"<EMAIL>",action:"DELETE_SUBMISSION",target:"Monthly Report Draft",targetType:"submission",status:"warning",details:"Deleted draft submission",ipAddress:"*************"},{id:"8",timestamp:"2024-01-20 13:55:10",user:"system",action:"BACKUP_DATABASE",target:"Database",targetType:"system",status:"success",details:"Automated database backup completed",ipAddress:"localhost"},{id:"9",timestamp:"2024-01-20 13:50:05",user:"<EMAIL>",action:"LOGIN_FAILED",target:"System",targetType:"system",status:"error",details:"Failed login attempt - invalid password",ipAddress:"*************"},{id:"10",timestamp:"2024-01-20 13:45:00",user:"admin",action:"UPDATE_SETTINGS",target:"System Settings",targetType:"system",status:"success",details:"Updated application configuration",ipAddress:"*************"}],p=[{value:"all",label:"All Actions"},{value:"LOGIN",label:"Login"},{value:"LOGOUT",label:"Logout"},{value:"CREATE_FORM",label:"Create Form"},{value:"UPDATE_FORM",label:"Update Form"},{value:"DELETE_FORM",label:"Delete Form"},{value:"SUBMIT_FORM",label:"Submit Form"},{value:"APPROVE_SUBMISSION",label:"Approve Submission"},{value:"REJECT_SUBMISSION",label:"Reject Submission"},{value:"CREATE_USER",label:"Create User"},{value:"UPDATE_USER",label:"Update User"},{value:"DELETE_USER",label:"Delete User"}],w=[{value:"all",label:"All Status"},{value:"success",label:"Success"},{value:"error",label:"Error"},{value:"warning",label:"Warning"},{value:"info",label:"Info"}],x=[{value:"all",label:"All Users"},{value:"admin",label:"Admin"},{value:"system",label:"System"},{value:"<EMAIL>",label:"Dev1"},{value:"<EMAIL>",label:"QC1"}],y=f=>{switch(f){case"success":return a.jsx(Dn,{className:"w-4 h-4 text-green-500"});case"error":return a.jsx(sc,{className:"w-4 h-4 text-red-500"});case"warning":return a.jsx(Ar,{className:"w-4 h-4 text-yellow-500"});case"info":return a.jsx(dl,{className:"w-4 h-4 text-blue-500"});default:return a.jsx(dl,{className:"w-4 h-4 text-gray-500"})}},N=f=>{switch(f){case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"warning":return"bg-yellow-100 text-yellow-800";case"info":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},h=f=>f.includes("FORM")?a.jsx(Le,{className:"w-4 h-4"}):f.includes("USER")?a.jsx(Mr,{className:"w-4 h-4"}):f.includes("LOGIN")||f.includes("LOGOUT")?a.jsx(Ms,{className:"w-4 h-4"}):a.jsx(Dl,{className:"w-4 h-4"}),m=g.filter(f=>{const k=f.target.toLowerCase().includes(t.toLowerCase())||f.details.toLowerCase().includes(t.toLowerCase())||f.user.toLowerCase().includes(t.toLowerCase()),E=r==="all"||f.action===r,C=l==="all"||f.status===l,S=i==="all"||f.user===i;return k&&E&&C&&S});return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Activity History"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Track all activities and changes in the system"})]}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsxs("button",{className:"flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:[a.jsx(Ml,{className:"w-4 h-4 mr-2"}),"Advanced Filters"]}),a.jsxs("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(cm,{className:"w-4 h-4 mr-2"}),"Export Log"]})]})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm p-6",children:a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(zl,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{type:"text",placeholder:"Search activities...",value:t,onChange:f=>n(f.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),a.jsx("select",{value:r,onChange:f=>s(f.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:p.map(f=>a.jsx("option",{value:f.value,children:f.label},f.value))}),a.jsx("select",{value:l,onChange:f=>o(f.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:w.map(f=>a.jsx("option",{value:f.value,children:f.label},f.value))}),a.jsx("select",{value:i,onChange:f=>u(f.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:x.map(f=>a.jsx("option",{value:f.value,children:f.label},f.value))}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Xr,{className:"w-4 h-4 text-gray-500"}),a.jsxs("select",{value:c,onChange:f=>d(f.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[a.jsx("option",{value:"today",children:"Today"}),a.jsx("option",{value:"yesterday",children:"Yesterday"}),a.jsx("option",{value:"last7days",children:"Last 7 days"}),a.jsx("option",{value:"last30days",children:"Last 30 days"}),a.jsx("option",{value:"last90days",children:"Last 90 days"})]})]})]})}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Activities"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:g.length})]}),a.jsx("div",{className:"p-3 bg-blue-100 rounded-full",children:a.jsx(Ms,{className:"w-6 h-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Successful"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:g.filter(f=>f.status==="success").length})]}),a.jsx("div",{className:"p-3 bg-green-100 rounded-full",children:a.jsx(Dn,{className:"w-6 h-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Errors"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:g.filter(f=>f.status==="error").length})]}),a.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:a.jsx(sc,{className:"w-6 h-6 text-red-600"})})]})}),a.jsx("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Warnings"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:g.filter(f=>f.status==="warning").length})]}),a.jsx("div",{className:"p-3 bg-yellow-100 rounded-full",children:a.jsx(Ar,{className:"w-6 h-6 text-yellow-600"})})]})})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:a.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Activity Log (",m.length," entries)"]})}),a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("table",{className:"w-full",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsxs("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Target"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Details"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IP Address"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:m.map(f=>a.jsxs("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:f.timestamp}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(Mr,{className:"w-4 h-4 text-gray-400 mr-2"}),a.jsx("span",{className:"text-sm font-medium text-gray-900",children:f.user})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:"flex items-center",children:[h(f.action),a.jsx("span",{className:"ml-2 text-sm text-gray-900",children:f.action.replace("_"," ")})]})}),a.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:f.target}),a.jsx("div",{className:"text-xs text-gray-500 capitalize",children:f.targetType})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsxs("div",{className:"flex items-center",children:[y(f.status),a.jsx("span",{className:`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${N(f.status)}`,children:f.status})]})}),a.jsx("td",{className:"px-6 py-4 text-sm text-gray-600 max-w-xs truncate",children:f.details}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:f.ipAddress})]},f.id))})]})})]}),m.length===0&&a.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[a.jsx(Ms,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No activities found"}),a.jsx("p",{className:"text-gray-600",children:t||r!=="all"||l!=="all"||i!=="all"?"Try adjusting your search or filters":"No activities have been recorded yet"})]})]})}function my({children:e}){const{user:t,isLoading:n}=nt();return n?a.jsx("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):t?a.jsx(a.Fragment,{children:e}):a.jsxs(Rf,{children:[a.jsx(Me,{path:"/register",element:a.jsx(ny,{})}),a.jsx(Me,{path:"*",element:a.jsx(ty,{})})]})}function py(){return a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx(ry,{}),a.jsxs("div",{className:"flex",children:[a.jsx(sy,{}),a.jsx("main",{className:"flex-1 p-6 overflow-auto",children:a.jsxs(Rf,{children:[a.jsx(Me,{path:"/dashboard",element:a.jsx(ly,{})}),a.jsx(Me,{path:"/departments",element:a.jsx(uy,{})}),a.jsx(Me,{path:"/forms",element:a.jsx(ay,{})}),a.jsx(Me,{path:"/forms/create",element:a.jsx(iy,{})}),a.jsx(Me,{path:"/checksheet/:formId",element:a.jsx(dy,{})}),a.jsx(Me,{path:"/reports",element:a.jsx(oy,{})}),a.jsx(Me,{path:"/history",element:a.jsx(fy,{})}),a.jsx(Me,{path:"/admin/users",element:a.jsx(cy,{})}),a.jsx(Me,{path:"/",element:a.jsx(wi,{to:"/dashboard",replace:!0})})]})})]})]})}function hy(){return _m.useEffect(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)})},[]),a.jsx(zx,{children:a.jsx(G0,{children:a.jsx(my,{children:a.jsx(py,{})})})})}vf(document.getElementById("root")).render(a.jsx(v.StrictMode,{children:a.jsx(hy,{})}));
