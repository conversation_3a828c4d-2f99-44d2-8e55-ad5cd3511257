const express = require('express');
const router = express.Router();
const foldersController = require('../controllers/foldersController');
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { validateFolderCreation, validateUUID } = require('../middleware/validation');

// Get all folders
router.get('/', 
  authenticateToken, 
  foldersController.getFolders
);

// Get single folder
router.get('/:id', 
  authenticateToken, 
  validateUUID('id'),
  foldersController.getFolderById
);

// Get folder path (breadcrumb)
router.get('/:id/path', 
  authenticateToken, 
  validateUUID('id'),
  foldersController.getFolderPath
);

// Create new folder
router.post('/', 
  authenticateToken, 
  requirePermission('create_folder'),
  validateFolderCreation,
  foldersController.createFolder
);

// Update folder
router.put('/:id', 
  authenticateToken, 
  requirePermission('create_folder'),
  validateUUID('id'),
  foldersController.updateFolder
);

// Delete folder
router.delete('/:id', 
  authenticateToken, 
  requirePermission('create_folder'),
  validateUUID('id'),
  foldersController.deleteFolder
);

module.exports = router;