import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Filter, 
  FileText, 
  Calendar,
  User,
  Eye,
  Edit,
  Trash2,
  FolderOpen,
  Play,
  Loader
} from 'lucide-react';
import { formsService, Form } from '../services/formsService';

interface FormsResponse {
  data: {
    forms: Form[];
    pagination: {
      pages: number;
      current: number;
      total: number;
    };
  };
}

export function Forms() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [forms, setForms] = useState<Form[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    let isMounted = true;
    
    const fetchForms = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await formsService.getForms({
          page,
          limit: 10,
          search: searchTerm || undefined,
          folder_id: selectedFolder === 'all' ? undefined : selectedFolder
        });
        
        if (isMounted) {
          setForms(response.data.forms);
          setTotalPages(response.data.pagination.pages);
        }
      } catch (err) {
        if (isMounted) {
          setError('Failed to fetch forms. Please try again later.');
          console.error('Error fetching forms:', err);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchForms();
    
    return () => {
      isMounted = false;
    };
  }, [page, searchTerm, selectedFolder]);

  const handleCreateForm = () => {
    navigate('/forms/create');
  };

  const handleEditForm = (id: string) => {
    navigate(`/forms/edit/${id}`);
  };

  const handleDeleteForm = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this form?')) {
      return;
    }

    try {
      await formsService.deleteForm(id);
      setForms(forms.filter(form => form.id !== id));
    } catch (err) {
      console.error('Error deleting form:', err);
      alert('Failed to delete form. Please try again later.');
    }
  };

  const folders = [
    { id: 'all', name: 'All Forms' },
    { id: 'safety', name: 'Safety Forms' },
    { id: 'maintenance', name: 'Maintenance' },
    { id: 'reports', name: 'Reports' }
  ];

  const filteredForms = forms.filter(form => {
    const matchesStatus = filterStatus === 'all' || form.status === filterStatus;
    return matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Forms</h1>
          <p className="text-gray-600 mt-1">Manage and organize your forms</p>
        </div>
        <button 
          onClick={handleCreateForm}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Create Form</span>
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 rounded-lg p-4">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search forms..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Folder Filter */}
          <div className="flex items-center space-x-2">
            <FolderOpen className="w-4 h-4 text-gray-500" />
            <select
              value={selectedFolder}
              onChange={(e) => setSelectedFolder(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {folders.map(folder => (
                <option key={folder.id} value={folder.id}>
                  {folder.name}
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>
      </div>

      {/* Forms Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredForms.map((form) => (
          <div key={form.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div className="p-6">
              {/* Form Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    form.type === 'excel' ? 'bg-green-100' : 'bg-blue-100'
                  }`}>
                    <FileText className={`w-5 h-5 ${
                      form.type === 'excel' ? 'text-green-600' : 'text-blue-600'
                    }`} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{form.name}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      form.status === 'active' ? 'bg-green-100 text-green-800' :
                      form.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {form.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* Form Description */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {form.description}
              </p>

              {/* Form Meta */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <FolderOpen className="w-4 h-4 mr-2" />
                  <span>{form.folder}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <User className="w-4 h-4 mr-2" />
                  <span>{form.created_by}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>{form.created_at}</span>
                </div>
              </div>

              {/* Submissions Count */}
              <div className="flex items-center justify-between mb-4">
                <span className="text-sm text-gray-600">
                  {form.submissions} submissions
                </span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  form.type === 'excel' ? 'bg-green-50 text-green-700' : 'bg-blue-50 text-blue-700'
                }`}>
                  {form.type.toUpperCase()}
                </span>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <button 
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    onClick={() => navigate(`/forms/${form.id}`)}
                    title="View Form"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => navigate(`/checksheet/${form.id}`)}
                    className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Perform Checksheet"
                  >
                    <Play className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => handleEditForm(form.id)}
                    className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Edit Form"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button 
                    onClick={() => handleDeleteForm(form.id)}
                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Delete Form"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
                <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredForms.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No forms found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || selectedFolder !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first form'
            }
          </p>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors">
            <Plus className="w-4 h-4" />
            <span>Create Form</span>
          </button>
        </div>
      )}
    </div>
  );
}