const { v4: uuidv4 } = require('uuid');
const database = require('../config/database');

// Get all forms accessible to user
const getForms = async (req, res) => {
  try {
    const user = req.user;
    const { page = 1, limit = 10, folder_id, search } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE f.is_active = 1';
    let params = {};

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        whereClause += ' AND f.general_department = @generalDepartment AND f.next_department = @nextDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        whereClause += ' AND f.general_department = @generalDepartment AND f.next_department = @nextDepartment AND f.direct_department = @directDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    // Folder filter
    if (folder_id) {
      if (folder_id === 'root') {
        whereClause += ' AND f.folder_id IS NULL';
      } else {
        whereClause += ' AND f.folder_id = @folderId';
        params.folderId = folder_id;
      }
    }

    // Search filter
    if (search) {
      whereClause += ' AND (f.name LIKE @search OR f.description LIKE @search)';
      params.search = `%${search}%`;
    }

    // Get forms with pagination
    const query = `
      SELECT 
        f.id, f.name, f.description, f.form_type, f.folder_id,
        f.general_department, f.next_department, f.direct_department,
        f.version, f.created_at, f.updated_at,
        u.username as created_by_name,
        fo.name as folder_name,
        (SELECT COUNT(*) FROM form_submissions fs WHERE fs.form_id = f.id) as submission_count
      FROM forms f
      LEFT JOIN users u ON f.created_by = u.id
      LEFT JOIN folders fo ON f.folder_id = fo.id
      ${whereClause}
      ORDER BY f.updated_at DESC
      OFFSET @offset ROWS
      FETCH NEXT @limit ROWS ONLY
    `;

    params.offset = offset;
    params.limit = parseInt(limit);

    const result = await database.query(query, params);

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM forms f
      ${whereClause}
    `;
    
    const countResult = await database.query(countQuery, params);
    const total = countResult.recordset[0].total;

    res.json({
      success: true,
      data: {
        forms: result.recordset,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get forms error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get forms'
    });
  }
};

// Get single form by ID
const getFormById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    let whereClause = 'WHERE f.id = @formId AND f.is_active = 1';
    let params = { formId: id };

    // Department access control
    if (user.role !== 'admin') {
      if (user.role === 'final_approval') {
        whereClause += ' AND f.general_department = @generalDepartment AND f.next_department = @nextDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
      } else {
        whereClause += ' AND f.general_department = @generalDepartment AND f.next_department = @nextDepartment AND f.direct_department = @directDepartment';
        params.generalDepartment = user.general_department;
        params.nextDepartment = user.next_department;
        params.directDepartment = user.direct_department;
      }
    }

    const query = `
      SELECT 
        f.id, f.name, f.description, f.form_type, f.folder_id,
        f.json_schema, f.excel_structure,
        f.general_department, f.next_department, f.direct_department,
        f.version, f.created_at, f.updated_at,
        u.username as created_by_name,
        fo.name as folder_name
      FROM forms f
      LEFT JOIN users u ON f.created_by = u.id
      LEFT JOIN folders fo ON f.folder_id = fo.id
      ${whereClause}
    `;

    const result = await database.query(query, params);

    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form not found or access denied'
      });
    }

    const form = result.recordset[0];

    // Parse JSON fields
    if (form.json_schema) {
      try {
        form.json_schema = JSON.parse(form.json_schema);
      } catch (error) {
        console.error('Error parsing JSON schema:', error);
      }
    }

    if (form.excel_structure) {
      try {
        form.excel_structure = JSON.parse(form.excel_structure);
      } catch (error) {
        console.error('Error parsing Excel structure:', error);
      }
    }

    res.json({
      success: true,
      data: { form }
    });

  } catch (error) {
    console.error('Get form by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get form'
    });
  }
};

// Create new form
const createForm = async (req, res) => {
  try {
    const {
      name,
      description,
      form_type = 'survey',
      folder_id,
      json_schema,
      excel_structure
    } = req.body;

    const user = req.user;
    const formId = uuidv4();

    // Validate folder access if folder_id provided
    if (folder_id) {
      const folderResult = await database.query(
        'SELECT id FROM folders WHERE id = @folderId AND general_department = @generalDepartment AND next_department = @nextDepartment AND direct_department = @directDepartment',
        {
          folderId: folder_id,
          generalDepartment: user.general_department,
          nextDepartment: user.next_department,
          directDepartment: user.direct_department
        }
      );

      if (folderResult.recordset.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Folder not found or access denied'
        });
      }
    }

    // Create form
    await database.query(`
      INSERT INTO forms (
        id, name, description, form_type, folder_id,
        json_schema, excel_structure,
        general_department, next_department, direct_department,
        created_by
      ) VALUES (
        @formId, @name, @description, @formType, @folderId,
        @jsonSchema, @excelStructure,
        @generalDepartment, @nextDepartment, @directDepartment,
        @createdBy
      )
    `, {
      formId,
      name,
      description,
      formType: form_type,
      folderId: folder_id || null,
      jsonSchema: json_schema ? JSON.stringify(json_schema) : null,
      excelStructure: excel_structure ? JSON.stringify(excel_structure) : null,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department,
      createdBy: user.id
    });

    // Get created form
    const formResult = await database.query(
      'SELECT id, name, description, form_type, folder_id, version, created_at FROM forms WHERE id = @formId',
      { formId }
    );

    res.status(201).json({
      success: true,
      message: 'Form created successfully',
      data: { form: formResult.recordset[0] }
    });

  } catch (error) {
    console.error('Create form error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create form'
    });
  }
};

// Update form
const updateForm = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      json_schema,
      excel_structure,
      is_active
    } = req.body;

    const user = req.user;

    // Check if form exists and user has access
    const existingForm = await database.query(`
      SELECT id, created_by, version 
      FROM forms 
      WHERE id = @formId 
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
    `, {
      formId: id,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (existingForm.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form not found or access denied'
      });
    }

    // Check if user can edit (creator or admin)
    if (user.role !== 'admin' && existingForm.recordset[0].created_by !== user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only edit forms you created'
      });
    }

    // Update form
    const updateFields = [];
    const params = { formId: id };

    if (name !== undefined) {
      updateFields.push('name = @name');
      params.name = name;
    }

    if (description !== undefined) {
      updateFields.push('description = @description');
      params.description = description;
    }

    if (json_schema !== undefined) {
      updateFields.push('json_schema = @jsonSchema');
      params.jsonSchema = json_schema ? JSON.stringify(json_schema) : null;
    }

    if (excel_structure !== undefined) {
      updateFields.push('excel_structure = @excelStructure');
      params.excelStructure = excel_structure ? JSON.stringify(excel_structure) : null;
    }

    if (is_active !== undefined) {
      updateFields.push('is_active = @isActive');
      params.isActive = is_active;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    updateFields.push('updated_at = GETDATE()');
    updateFields.push('version = version + 1');

    const updateQuery = `
      UPDATE forms 
      SET ${updateFields.join(', ')}
      WHERE id = @formId
    `;

    await database.query(updateQuery, params);

    // Get updated form
    const updatedForm = await database.query(
      'SELECT id, name, description, form_type, version, updated_at FROM forms WHERE id = @formId',
      { formId: id }
    );

    res.json({
      success: true,
      message: 'Form updated successfully',
      data: { form: updatedForm.recordset[0] }
    });

  } catch (error) {
    console.error('Update form error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update form'
    });
  }
};

// Delete form
const deleteForm = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Check if form exists and user has access
    const existingForm = await database.query(`
      SELECT id, created_by, name
      FROM forms 
      WHERE id = @formId 
        AND general_department = @generalDepartment 
        AND next_department = @nextDepartment 
        AND direct_department = @directDepartment
    `, {
      formId: id,
      generalDepartment: user.general_department,
      nextDepartment: user.next_department,
      directDepartment: user.direct_department
    });

    if (existingForm.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Form not found or access denied'
      });
    }

    // Check if user can delete (creator or admin)
    if (user.role !== 'admin' && existingForm.recordset[0].created_by !== user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete forms you created'
      });
    }

    // Check if form has submissions
    const submissionCount = await database.query(
      'SELECT COUNT(*) as count FROM form_submissions WHERE form_id = @formId',
      { formId: id }
    );

    if (submissionCount.recordset[0].count > 0) {
      // Soft delete - just mark as inactive
      await database.query(
        'UPDATE forms SET is_active = 0, updated_at = GETDATE() WHERE id = @formId',
        { formId: id }
      );

      return res.json({
        success: true,
        message: 'Form deactivated successfully (has existing submissions)'
      });
    }

    // Hard delete if no submissions
    await database.query('DELETE FROM forms WHERE id = @formId', { formId: id });

    res.json({
      success: true,
      message: 'Form deleted successfully'
    });

  } catch (error) {
    console.error('Delete form error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete form'
    });
  }
};

module.exports = {
  getForms,
  getFormById,
  createForm,
  updateForm,
  deleteForm
};