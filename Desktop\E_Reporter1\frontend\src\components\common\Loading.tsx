import { Loader } from 'lucide-react';

interface LoadingProps {
  className?: string;
  message?: string;
}

export function Loading({ className = '', message = 'Loading...' }: LoadingProps) {
  return (
    <div className={`flex flex-col items-center justify-center p-4 ${className}`}>
      <Loader className="w-8 h-8 animate-spin text-blue-600" />
      <p className="mt-2 text-gray-600">{message}</p>
    </div>
  );
}
