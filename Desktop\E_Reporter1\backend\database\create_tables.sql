-- FieldReporter Database Schema
-- SQL Server 2016+

-- Create database if not exists
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'FieldReporter')
BEGIN
    CREATE DATABASE FieldReporter;
END
GO

USE FieldReporter;
GO

-- Create departments table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='departments' AND xtype='U')
BEGIN
    CREATE TABLE departments (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        type NVARCHAR(50) NOT NULL CHECK (type IN ('general', 'next', 'direct')),
        parent_id UNIQUEIDENTIFIER NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (parent_id) REFERENCES departments(id)
    );
END
GO

-- Create users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        username NVARCHAR(100) NOT NULL UNIQUE,
        email NVARCHAR(255) NOT NULL UNIQUE,
        password_hash NVARCHAR(255) NOT NULL,
        role NVARCHAR(50) NOT NULL DEFAULT 'submit' CHECK (role IN ('admin', 'submit', 'approval', 'final_approval')),
        general_department NVARCHAR(255) NOT NULL,
        next_department NVARCHAR(255) NOT NULL,
        direct_department NVARCHAR(255) NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Create folders table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='folders' AND xtype='U')
BEGIN
    CREATE TABLE folders (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX),
        parent_id UNIQUEIDENTIFIER NULL,
        general_department NVARCHAR(255) NOT NULL,
        next_department NVARCHAR(255) NOT NULL,
        direct_department NVARCHAR(255) NOT NULL,
        created_by UNIQUEIDENTIFIER NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (parent_id) REFERENCES folders(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
    );
END
GO

-- Create forms table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='forms' AND xtype='U')
BEGIN
    CREATE TABLE forms (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX),
        folder_id UNIQUEIDENTIFIER NULL,
        form_type NVARCHAR(50) NOT NULL DEFAULT 'survey' CHECK (form_type IN ('survey', 'excel', 'custom')),
        json_schema NVARCHAR(MAX),
        excel_structure NVARCHAR(MAX),
        general_department NVARCHAR(255) NOT NULL,
        next_department NVARCHAR(255) NOT NULL,
        direct_department NVARCHAR(255) NOT NULL,
        created_by UNIQUEIDENTIFIER NOT NULL,
        version INT DEFAULT 1,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (folder_id) REFERENCES folders(id),
        FOREIGN KEY (created_by) REFERENCES users(id)
    );
END
GO

-- Create form_submissions table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='form_submissions' AND xtype='U')
BEGIN
    CREATE TABLE form_submissions (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        form_id UNIQUEIDENTIFIER NOT NULL,
        user_id UNIQUEIDENTIFIER NOT NULL,
        submission_data NVARCHAR(MAX),
        status NVARCHAR(50) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved', 'rejected', 'final_approved')),
        approval_notes NVARCHAR(MAX),
        approved_by UNIQUEIDENTIFIER NULL,
        final_approved_by UNIQUEIDENTIFIER NULL,
        latitude DECIMAL(10, 8) NULL,
        longitude DECIMAL(11, 8) NULL,
        location_name NVARCHAR(255),
        time_spent INT DEFAULT 0,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (form_id) REFERENCES forms(id),
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (approved_by) REFERENCES users(id),
        FOREIGN KEY (final_approved_by) REFERENCES users(id)
    );
END
GO

-- Create submission_attachments table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='submission_attachments' AND xtype='U')
BEGIN
    CREATE TABLE submission_attachments (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        submission_id UNIQUEIDENTIFIER NOT NULL,
        file_name NVARCHAR(255) NOT NULL,
        file_path NVARCHAR(500) NOT NULL,
        file_size BIGINT NOT NULL,
        file_type NVARCHAR(50),
        mime_type NVARCHAR(100),
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (submission_id) REFERENCES form_submissions(id)
    );
END
GO

-- Create user_sessions table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_sessions' AND xtype='U')
BEGIN
    CREATE TABLE user_sessions (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER NOT NULL,
        session_token NVARCHAR(500) NOT NULL,
        refresh_token NVARCHAR(500) NOT NULL,
        ip_address NVARCHAR(45),
        user_agent NVARCHAR(500),
        expires_at DATETIME2 NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
END
GO

-- Create audit_logs table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='audit_logs' AND xtype='U')
BEGIN
    CREATE TABLE audit_logs (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER NULL,
        action NVARCHAR(50) NOT NULL,
        table_name NVARCHAR(100) NOT NULL,
        record_id UNIQUEIDENTIFIER,
        old_values NVARCHAR(MAX),
        new_values NVARCHAR(MAX),
        ip_address NVARCHAR(45),
        user_agent NVARCHAR(500),
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
END
GO

-- Create system_settings table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='system_settings' AND xtype='U')
BEGIN
    CREATE TABLE system_settings (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        setting_key NVARCHAR(100) NOT NULL UNIQUE,
        setting_value NVARCHAR(MAX),
        setting_type NVARCHAR(50) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
        is_public BIT DEFAULT 0,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Create notifications table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='notifications' AND xtype='U')
BEGIN
    CREATE TABLE notifications (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER NOT NULL,
        title NVARCHAR(255) NOT NULL,
        message NVARCHAR(MAX),
        type NVARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
        related_id UNIQUEIDENTIFIER NULL,
        related_type NVARCHAR(100),
        is_read BIT DEFAULT 0,
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
END
GO

-- Insert sample departments
IF NOT EXISTS (SELECT * FROM departments WHERE name = 'Tổng Công Ty')
BEGIN
    INSERT INTO departments (name, type) VALUES 
    ('Tổng Công Ty', 'general'),
    ('Phòng IT', 'next'),
    ('Team Development', 'direct'),
    ('Phòng Nhân Sự', 'next'),
    ('Team Tuyển Dụng', 'direct');
END
GO

-- Insert default admin user
IF NOT EXISTS (SELECT * FROM users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO users (username, email, password_hash, role, general_department, next_department, direct_department)
    VALUES (
        'admin',
        '<EMAIL>',
        '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', -- admin123
        'admin',
        'Tổng Công Ty',
        'Phòng IT',
        'Team Development'
    );
END
GO

-- Create indexes for performance
CREATE NONCLUSTERED INDEX IX_users_email ON users(email);
CREATE NONCLUSTERED INDEX IX_users_username ON users(username);
CREATE NONCLUSTERED INDEX IX_forms_folder_id ON forms(folder_id);
CREATE NONCLUSTERED INDEX IX_form_submissions_form_id ON form_submissions(form_id);
CREATE NONCLUSTERED INDEX IX_form_submissions_user_id ON form_submissions(user_id);
CREATE NONCLUSTERED INDEX IX_form_submissions_status ON form_submissions(status);
CREATE NONCLUSTERED INDEX IX_user_sessions_user_id ON user_sessions(user_id);
CREATE NONCLUSTERED INDEX IX_audit_logs_user_id ON audit_logs(user_id);
CREATE NONCLUSTERED INDEX IX_audit_logs_table_name ON audit_logs(table_name);
CREATE NONCLUSTERED INDEX IX_notifications_user_id ON notifications(user_id);

PRINT 'FieldReporter database schema created successfully!';
PRINT 'Default admin user: <EMAIL> / admin123';
GO
