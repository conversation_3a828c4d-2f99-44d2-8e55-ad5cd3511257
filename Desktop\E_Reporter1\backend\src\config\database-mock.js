// Mock database for testing without SQL Server
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

class MockDatabase {
  constructor() {
    this.connected = false;
    this.users = [
      {
        id: '550e8400-e29b-41d4-a716-446655440000',
        username: 'admin',
        email: '<EMAIL>',
        password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', // admin123
        role: 'admin',
        general_department: 'Tổng Công Ty',
        next_department: 'Phòng IT',
        direct_department: 'Team Development',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        username: 'manager',
        email: '<EMAIL>',
        password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', // admin123
        role: 'final_approval',
        general_department: 'Tổng Công Ty',
        next_department: 'Phòng IT',
        direct_department: 'Team Development',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        username: 'dev1',
        email: '<EMAIL>',
        password_hash: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', // admin123
        role: 'submit',
        general_department: 'Tổng Công Ty',
        next_department: 'Phòng IT',
        direct_department: 'Team Development',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
    
    this.forms = [];
    this.folders = [];
    this.submissions = [];
    this.sessions = [];
  }

  async connect() {
    try {
      this.connected = true;
      console.log('✅ Connected to Mock Database (for testing)');
      console.log('✅ Mock database connection test successful');
      return this;
    } catch (error) {
      console.error('❌ Mock database connection failed:', error.message);
      this.connected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      this.connected = false;
      console.log('✅ Disconnected from mock database');
    } catch (error) {
      console.error('❌ Error disconnecting from mock database:', error.message);
    }
  }

  getPool() {
    if (!this.connected) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this;
  }

  async query(queryString, params = {}) {
    try {
      console.log('Mock Query:', queryString, params);
      
      // Mock user queries
      if (queryString.includes('SELECT') && queryString.includes('users')) {
        if (queryString.includes('email = @email') || queryString.includes('username = @username')) {
          const user = this.users.find(u => 
            u.email === params.email || u.username === params.username
          );
          return { recordset: user ? [user] : [] };
        }
        
        if (queryString.includes('id = @userId')) {
          const user = this.users.find(u => u.id === params.userId);
          return { recordset: user ? [user] : [] };
        }
        
        return { recordset: this.users };
      }
      
      // Mock insert user
      if (queryString.includes('INSERT INTO users')) {
        const newUser = {
          id: require('uuid').v4(),
          username: params.username,
          email: params.email,
          password_hash: params.password_hash,
          role: params.role || 'submit',
          general_department: params.general_department,
          next_department: params.next_department,
          direct_department: params.direct_department,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        this.users.push(newUser);
        return { recordset: [{ id: newUser.id }] };
      }
      
      // Mock session queries
      if (queryString.includes('user_sessions')) {
        if (queryString.includes('INSERT')) {
          const session = {
            id: require('uuid').v4(),
            user_id: params.user_id,
            session_token: params.session_token,
            refresh_token: params.refresh_token,
            ip_address: params.ip_address,
            user_agent: params.user_agent,
            expires_at: params.expires_at,
            is_active: true,
            created_at: new Date().toISOString()
          };
          this.sessions.push(session);
          return { recordset: [session] };
        }
        
        if (queryString.includes('UPDATE')) {
          const session = this.sessions.find(s => s.refresh_token === params.refresh_token);
          if (session) {
            session.is_active = false;
          }
          return { recordset: [] };
        }
        
        if (queryString.includes('SELECT')) {
          const session = this.sessions.find(s => 
            s.refresh_token === params.refresh_token && s.is_active
          );
          return { recordset: session ? [session] : [] };
        }
      }
      
      // Default empty result
      return { recordset: [] };
    } catch (error) {
      console.error('❌ Mock database query error:', error.message);
      throw error;
    }
  }

  async execute(procedureName, params = {}) {
    try {
      console.log('Mock Procedure:', procedureName, params);
      return { recordset: [] };
    } catch (error) {
      console.error('❌ Mock stored procedure execution error:', error.message);
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: 'Mock Database v1.0',
        connected: this.connected
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        connected: false
      };
    }
  }
}

// Create singleton instance
const database = new MockDatabase();

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Gracefully shutting down mock database connection...');
  await database.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔄 Gracefully shutting down mock database connection...');
  await database.disconnect();
  process.exit(0);
});

module.exports = database;
