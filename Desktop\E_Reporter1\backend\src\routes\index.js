const express = require('express');
const router = express.Router();

// Import route modules
const authRoutes = require('./auth');
const formsRoutes = require('./forms');
const foldersRoutes = require('./folders');
const submissionsRoutes = require('./submissions');
const checksheetRoutes = require('./checksheet');

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'FieldReporter API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes
router.use('/auth', authRoutes);
router.use('/forms', formsRoutes);
router.use('/folders', foldersRoutes);
router.use('/submissions', submissionsRoutes);
router.use('/checksheet', checksheetRoutes);

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

module.exports = router;