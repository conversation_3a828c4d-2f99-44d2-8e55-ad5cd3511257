const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const database = require('../config/database');

// Generate JWT tokens
const generateTokens = (userId) => {
  const accessToken = jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );

  const refreshToken = jwt.sign(
    { userId },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );

  return { accessToken, refreshToken };
};

// Register new user
const register = async (req, res) => {
  try {
    const {
      username,
      email,
      password,
      role = 'submit',
      general_department,
      next_department,
      direct_department
    } = req.body;

    // Check if user already exists
    const existingUser = await database.query(
      'SELECT id FROM users WHERE email = @email OR username = @username',
      { email, username }
    );

    if (existingUser.recordset.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'User with this email or username already exists'
      });
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const userId = uuidv4();
    await database.query(`
      INSERT INTO users (
        id, username, email, password_hash, role,
        general_department, next_department, direct_department
      ) VALUES (
        @userId, @username, @email, @passwordHash, @role,
        @generalDepartment, @nextDepartment, @directDepartment
      )
    `, {
      userId,
      username,
      email,
      passwordHash,
      role,
      generalDepartment: general_department,
      nextDepartment: next_department,
      directDepartment: direct_department
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(userId);

    // Save refresh token to database
    const sessionId = uuidv4();
    await database.query(`
      INSERT INTO user_sessions (
        id, user_id, session_token, refresh_token, 
        ip_address, user_agent, expires_at
      ) VALUES (
        @sessionId, @userId, @sessionToken, @refreshToken,
        @ipAddress, @userAgent, @expiresAt
      )
    `, {
      sessionId,
      userId,
      sessionToken: accessToken,
      refreshToken,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });

    // Get created user (without password)
    const userResult = await database.query(
      'SELECT id, username, email, role, general_department, next_department, direct_department, created_at FROM users WHERE id = @userId',
      { userId }
    );

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userResult.recordset[0],
        accessToken,
        refreshToken
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Get user from database
    const userResult = await database.query(
      'SELECT id, username, email, password_hash, role, general_department, next_department, direct_department, is_active FROM users WHERE email = @email',
      { email }
    );

    if (userResult.recordset.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    const user = userResult.recordset[0];

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is inactive'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user.id);

    // Save session to database
    const sessionId = uuidv4();
    await database.query(`
      INSERT INTO user_sessions (
        id, user_id, session_token, refresh_token,
        ip_address, user_agent, expires_at
      ) VALUES (
        @sessionId, @userId, @sessionToken, @refreshToken,
        @ipAddress, @userAgent, @expiresAt
      )
    `, {
      sessionId,
      userId: user.id,
      sessionToken: accessToken,
      refreshToken,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });

    // Update last login
    await database.query(
      'UPDATE users SET last_login = GETDATE() WHERE id = @userId',
      { userId: user.id }
    );

    // Remove password from response
    delete user.password_hash;

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user,
        accessToken,
        refreshToken
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Refresh token
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Refresh token required'
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Check if session exists and is active
    const sessionResult = await database.query(
      'SELECT user_id FROM user_sessions WHERE refresh_token = @refreshToken AND is_active = 1 AND expires_at > GETDATE()',
      { refreshToken }
    );

    if (sessionResult.recordset.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token'
      });
    }

    const userId = sessionResult.recordset[0].user_id;

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(userId);

    // Update session with new tokens
    await database.query(`
      UPDATE user_sessions 
      SET session_token = @accessToken, 
          refresh_token = @newRefreshToken,
          last_accessed = GETDATE()
      WHERE refresh_token = @oldRefreshToken
    `, {
      accessToken,
      newRefreshToken,
      oldRefreshToken: refreshToken
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        accessToken,
        refreshToken: newRefreshToken
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(401).json({
      success: false,
      message: 'Token refresh failed'
    });
  }
};

// Logout user
const logout = async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      // Deactivate session
      await database.query(
        'UPDATE user_sessions SET is_active = 0 WHERE session_token = @token',
        { token }
      );
    }

    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed'
    });
  }
};

// Get current user profile
const getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const userResult = await database.query(
      'SELECT id, username, email, role, general_department, next_department, direct_department, created_at, last_login FROM users WHERE id = @userId',
      { userId }
    );

    if (userResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        user: userResult.recordset[0]
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user profile'
    });
  }
};

module.exports = {
  register,
  login,
  refreshToken,
  logout,
  getProfile
};