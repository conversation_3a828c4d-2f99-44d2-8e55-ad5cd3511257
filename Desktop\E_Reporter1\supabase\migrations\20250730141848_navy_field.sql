-- Add Checksheet Tables and Database Views
-- Created: 2024-01-20
-- Description: Add checksheet functionality and database views for better data relationships

USE FieldReporter;
GO

-- =============================================
-- 1. CHECKSHEET SESSIONS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='checksheet_sessions' AND xtype='U')
BEGIN
    CREATE TABLE checksheet_sessions (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        form_id UNIQUEIDENTIFIER NOT NULL,
        user_id UNIQUEIDENTIFIER NOT NULL,
        session_name NVARCHAR(255) NOT NULL,
        status NVARCHAR(50) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'paused', 'cancelled')),
        started_at DATETIME2 DEFAULT GETDATE(),
        completed_at DATETIME2 NULL,
        total_questions INT DEFAULT 0,
        answered_questions INT DEFAULT 0,
        progress_percentage DECIMAL(5,2) DEFAULT 0.00,
        latitude DECIMAL(10, 8) NULL,
        longitude DECIMAL(11, 8) NULL,
        location_name NVARCHAR(255) NULL,
        device_info NVARCHAR(MAX) NULL, -- JSON data about device
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (form_id) REFERENCES forms(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_checksheet_sessions_form ON checksheet_sessions(form_id);
    CREATE INDEX IX_checksheet_sessions_user ON checksheet_sessions(user_id);
    CREATE INDEX IX_checksheet_sessions_status ON checksheet_sessions(status);
    CREATE INDEX IX_checksheet_sessions_started ON checksheet_sessions(started_at);
    
    PRINT 'Table [checksheet_sessions] created successfully';
END
GO

-- =============================================
-- 2. CHECKSHEET RESPONSES TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='checksheet_responses' AND xtype='U')
BEGIN
    CREATE TABLE checksheet_responses (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        session_id UNIQUEIDENTIFIER NOT NULL,
        question_id NVARCHAR(100) NOT NULL, -- From form JSON schema
        question_text NVARCHAR(MAX) NOT NULL,
        question_type NVARCHAR(50) NOT NULL, -- text, radio, checkbox, dropdown, rating, etc.
        response_value NVARCHAR(MAX) NULL, -- The actual response
        response_data NVARCHAR(MAX) NULL, -- JSON for complex responses
        is_required BIT DEFAULT 0,
        is_answered BIT DEFAULT 0,
        answered_at DATETIME2 NULL,
        validation_status NVARCHAR(50) DEFAULT 'valid' CHECK (validation_status IN ('valid', 'invalid', 'pending')),
        validation_message NVARCHAR(500) NULL,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (session_id) REFERENCES checksheet_sessions(id) ON DELETE CASCADE
    );
    
    -- Create indexes
    CREATE INDEX IX_checksheet_responses_session ON checksheet_responses(session_id);
    CREATE INDEX IX_checksheet_responses_question ON checksheet_responses(question_id);
    CREATE INDEX IX_checksheet_responses_answered ON checksheet_responses(is_answered);
    
    PRINT 'Table [checksheet_responses] created successfully';
END
GO

-- =============================================
-- 3. CHECKSHEET ATTACHMENTS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='checksheet_attachments' AND xtype='U')
BEGIN
    CREATE TABLE checksheet_attachments (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        session_id UNIQUEIDENTIFIER NOT NULL,
        response_id UNIQUEIDENTIFIER NULL, -- Optional: attach to specific response
        file_name NVARCHAR(255) NOT NULL,
        file_path NVARCHAR(500) NOT NULL,
        file_size BIGINT NOT NULL,
        file_type NVARCHAR(100) NOT NULL,
        mime_type NVARCHAR(100) NOT NULL,
        description NVARCHAR(500) NULL,
        uploaded_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (session_id) REFERENCES checksheet_sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (response_id) REFERENCES checksheet_responses(id) ON DELETE SET NULL
    );
    
    -- Create indexes
    CREATE INDEX IX_checksheet_attachments_session ON checksheet_attachments(session_id);
    CREATE INDEX IX_checksheet_attachments_response ON checksheet_attachments(response_id);
    CREATE INDEX IX_checksheet_attachments_type ON checksheet_attachments(file_type);
    
    PRINT 'Table [checksheet_attachments] created successfully';
END
GO

-- =============================================
-- 4. FORM TEMPLATES TABLE (for reusable templates)
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='form_templates' AND xtype='U')
BEGIN
    CREATE TABLE form_templates (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        name NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX) NULL,
        category NVARCHAR(100) NOT NULL, -- safety, maintenance, quality, hr, etc.
        template_data NVARCHAR(MAX) NOT NULL, -- JSON template structure
        is_public BIT DEFAULT 0, -- Can be used by other departments
        usage_count INT DEFAULT 0,
        created_by UNIQUEIDENTIFIER NOT NULL,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (created_by) REFERENCES users(id)
    );
    
    -- Create indexes
    CREATE INDEX IX_form_templates_category ON form_templates(category);
    CREATE INDEX IX_form_templates_public ON form_templates(is_public);
    CREATE INDEX IX_form_templates_created_by ON form_templates(created_by);
    
    PRINT 'Table [form_templates] created successfully';
END
GO

-- =============================================
-- 5. DATABASE VIEWS FOR BETTER DATA RELATIONSHIPS
-- =============================================

-- View: Complete Form Information with Creator Details
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_forms_complete')
    DROP VIEW vw_forms_complete;
GO

CREATE VIEW vw_forms_complete AS
SELECT 
    f.id,
    f.name,
    f.description,
    f.form_type,
    f.folder_id,
    fo.name as folder_name,
    f.general_department,
    f.next_department,
    f.direct_department,
    f.created_by,
    u.username as created_by_name,
    u.email as created_by_email,
    f.is_active,
    f.version,
    f.created_at,
    f.updated_at,
    -- Statistics
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.form_id = f.id) as total_submissions,
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.form_id = f.id AND fs.status = 'submitted') as pending_submissions,
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.form_id = f.id AND fs.status = 'approved') as approved_submissions,
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.form_id = f.id AND fs.status = 'final_approved') as final_approved_submissions,
    (SELECT COUNT(*) FROM checksheet_sessions cs WHERE cs.form_id = f.id) as total_checksheet_sessions,
    (SELECT COUNT(*) FROM checksheet_sessions cs WHERE cs.form_id = f.id AND cs.status = 'completed') as completed_checksheet_sessions
FROM forms f
LEFT JOIN folders fo ON f.folder_id = fo.id
LEFT JOIN users u ON f.created_by = u.id;
GO

-- View: User Activity Summary
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_user_activity_summary')
    DROP VIEW vw_user_activity_summary;
GO

CREATE VIEW vw_user_activity_summary AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.role,
    u.general_department,
    u.next_department,
    u.direct_department,
    u.is_active,
    u.last_login,
    -- Form Statistics
    (SELECT COUNT(*) FROM forms f WHERE f.created_by = u.id) as forms_created,
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.user_id = u.id) as submissions_made,
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.approved_by = u.id) as submissions_approved,
    (SELECT COUNT(*) FROM form_submissions fs WHERE fs.final_approved_by = u.id) as submissions_final_approved,
    -- Checksheet Statistics
    (SELECT COUNT(*) FROM checksheet_sessions cs WHERE cs.user_id = u.id) as checksheet_sessions,
    (SELECT COUNT(*) FROM checksheet_sessions cs WHERE cs.user_id = u.id AND cs.status = 'completed') as checksheet_completed,
    -- Recent Activity
    (SELECT MAX(fs.created_at) FROM form_submissions fs WHERE fs.user_id = u.id) as last_submission,
    (SELECT MAX(cs.started_at) FROM checksheet_sessions cs WHERE cs.user_id = u.id) as last_checksheet_session
FROM users u;
GO

-- View: Department Statistics
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_department_statistics')
    DROP VIEW vw_department_statistics;
GO

CREATE VIEW vw_department_statistics AS
SELECT 
    general_department,
    next_department,
    direct_department,
    -- User counts
    COUNT(DISTINCT u.id) as total_users,
    SUM(CASE WHEN u.is_active = 1 THEN 1 ELSE 0 END) as active_users,
    -- Form counts
    COUNT(DISTINCT f.id) as total_forms,
    SUM(CASE WHEN f.is_active = 1 THEN 1 ELSE 0 END) as active_forms,
    -- Submission counts
    COUNT(DISTINCT fs.id) as total_submissions,
    SUM(CASE WHEN fs.status = 'final_approved' THEN 1 ELSE 0 END) as approved_submissions,
    -- Folder counts
    COUNT(DISTINCT fo.id) as total_folders
FROM users u
LEFT JOIN forms f ON f.general_department = u.general_department 
    AND f.next_department = u.next_department 
    AND f.direct_department = u.direct_department
LEFT JOIN form_submissions fs ON fs.form_id = f.id
LEFT JOIN folders fo ON fo.general_department = u.general_department 
    AND fo.next_department = u.next_department 
    AND fo.direct_department = u.direct_department
GROUP BY general_department, next_department, direct_department;
GO

-- View: Checksheet Session Details
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_checksheet_sessions_complete')
    DROP VIEW vw_checksheet_sessions_complete;
GO

CREATE VIEW vw_checksheet_sessions_complete AS
SELECT 
    cs.id,
    cs.session_name,
    cs.status,
    cs.started_at,
    cs.completed_at,
    cs.progress_percentage,
    cs.location_name,
    -- Form details
    f.id as form_id,
    f.name as form_name,
    f.form_type,
    -- User details
    u.id as user_id,
    u.username,
    u.email,
    u.general_department,
    u.next_department,
    u.direct_department,
    -- Response statistics
    cs.total_questions,
    cs.answered_questions,
    (SELECT COUNT(*) FROM checksheet_responses cr WHERE cr.session_id = cs.id AND cr.is_answered = 1) as actual_answered,
    (SELECT COUNT(*) FROM checksheet_attachments ca WHERE ca.session_id = cs.id) as attachment_count,
    -- Time calculations
    CASE 
        WHEN cs.completed_at IS NOT NULL THEN DATEDIFF(MINUTE, cs.started_at, cs.completed_at)
        ELSE DATEDIFF(MINUTE, cs.started_at, GETDATE())
    END as duration_minutes
FROM checksheet_sessions cs
LEFT JOIN forms f ON cs.form_id = f.id
LEFT JOIN users u ON cs.user_id = u.id;
GO

-- =============================================
-- 6. STORED PROCEDURES FOR CHECKSHEET OPERATIONS
-- =============================================

-- Procedure to start a new checksheet session
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_StartChecksheetSession')
    DROP PROCEDURE sp_StartChecksheetSession;
GO

CREATE PROCEDURE sp_StartChecksheetSession
    @FormId UNIQUEIDENTIFIER,
    @UserId UNIQUEIDENTIFIER,
    @SessionName NVARCHAR(255),
    @Latitude DECIMAL(10,8) = NULL,
    @Longitude DECIMAL(11,8) = NULL,
    @LocationName NVARCHAR(255) = NULL,
    @DeviceInfo NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @SessionId UNIQUEIDENTIFIER = NEWID();
    DECLARE @TotalQuestions INT = 0;
    
    -- Get total questions from form JSON schema (simplified - would need JSON parsing in real implementation)
    SELECT @TotalQuestions = 10; -- Placeholder - would parse JSON schema
    
    -- Create checksheet session
    INSERT INTO checksheet_sessions (
        id, form_id, user_id, session_name, total_questions,
        latitude, longitude, location_name, device_info
    ) VALUES (
        @SessionId, @FormId, @UserId, @SessionName, @TotalQuestions,
        @Latitude, @Longitude, @LocationName, @DeviceInfo
    );
    
    -- Return session details
    SELECT 
        cs.*,
        f.name as form_name,
        f.json_schema,
        f.excel_structure
    FROM checksheet_sessions cs
    LEFT JOIN forms f ON cs.form_id = f.id
    WHERE cs.id = @SessionId;
END
GO

-- Procedure to save checksheet response
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_SaveChecksheetResponse')
    DROP PROCEDURE sp_SaveChecksheetResponse;
GO

CREATE PROCEDURE sp_SaveChecksheetResponse
    @SessionId UNIQUEIDENTIFIER,
    @QuestionId NVARCHAR(100),
    @QuestionText NVARCHAR(MAX),
    @QuestionType NVARCHAR(50),
    @ResponseValue NVARCHAR(MAX),
    @ResponseData NVARCHAR(MAX) = NULL,
    @IsRequired BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Insert or update response
    IF EXISTS (SELECT 1 FROM checksheet_responses WHERE session_id = @SessionId AND question_id = @QuestionId)
    BEGIN
        UPDATE checksheet_responses 
        SET response_value = @ResponseValue,
            response_data = @ResponseData,
            is_answered = 1,
            answered_at = GETDATE(),
            updated_at = GETDATE()
        WHERE session_id = @SessionId AND question_id = @QuestionId;
    END
    ELSE
    BEGIN
        INSERT INTO checksheet_responses (
            session_id, question_id, question_text, question_type,
            response_value, response_data, is_required, is_answered, answered_at
        ) VALUES (
            @SessionId, @QuestionId, @QuestionText, @QuestionType,
            @ResponseValue, @ResponseData, @IsRequired, 1, GETDATE()
        );
    END
    
    -- Update session progress
    DECLARE @AnsweredCount INT;
    DECLARE @TotalCount INT;
    DECLARE @ProgressPercentage DECIMAL(5,2);
    
    SELECT @AnsweredCount = COUNT(*) FROM checksheet_responses WHERE session_id = @SessionId AND is_answered = 1;
    SELECT @TotalCount = total_questions FROM checksheet_sessions WHERE id = @SessionId;
    
    SET @ProgressPercentage = CASE WHEN @TotalCount > 0 THEN (@AnsweredCount * 100.0) / @TotalCount ELSE 0 END;
    
    UPDATE checksheet_sessions 
    SET answered_questions = @AnsweredCount,
        progress_percentage = @ProgressPercentage,
        updated_at = GETDATE()
    WHERE id = @SessionId;
END
GO

-- Procedure to complete checksheet session
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CompleteChecksheetSession')
    DROP PROCEDURE sp_CompleteChecksheetSession;
GO

CREATE PROCEDURE sp_CompleteChecksheetSession
    @SessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Update session status
    UPDATE checksheet_sessions 
    SET status = 'completed',
        completed_at = GETDATE(),
        progress_percentage = 100.00,
        updated_at = GETDATE()
    WHERE id = @SessionId;
    
    -- Create form submission from checksheet session
    DECLARE @FormId UNIQUEIDENTIFIER;
    DECLARE @UserId UNIQUEIDENTIFIER;
    DECLARE @SubmissionData NVARCHAR(MAX);
    DECLARE @LocationName NVARCHAR(255);
    DECLARE @Latitude DECIMAL(10,8);
    DECLARE @Longitude DECIMAL(11,8);
    
    SELECT @FormId = form_id, @UserId = user_id, @LocationName = location_name,
           @Latitude = latitude, @Longitude = longitude
    FROM checksheet_sessions WHERE id = @SessionId;
    
    -- Build submission data from responses (simplified JSON)
    SELECT @SubmissionData = (
        SELECT question_id, response_value, answered_at
        FROM checksheet_responses 
        WHERE session_id = @SessionId AND is_answered = 1
        FOR JSON AUTO
    );
    
    -- Create form submission
    INSERT INTO form_submissions (
        form_id, user_id, submission_data, status,
        latitude, longitude, location_name
    ) VALUES (
        @FormId, @UserId, @SubmissionData, 'submitted',
        @Latitude, @Longitude, @LocationName
    );
    
    -- Return completed session
    SELECT * FROM vw_checksheet_sessions_complete WHERE id = @SessionId;
END
GO

-- =============================================
-- 7. SAMPLE FORM TEMPLATES
-- =============================================

-- Insert sample form templates
IF NOT EXISTS (SELECT * FROM form_templates WHERE name = 'Basic Safety Inspection')
BEGIN
    DECLARE @AdminId UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');
    
    INSERT INTO form_templates (name, description, category, template_data, is_public, created_by) VALUES
    ('Basic Safety Inspection', 'Standard safety inspection template for all departments', 'safety', 
    '{
        "title": "Safety Inspection Checklist",
        "pages": [{
            "name": "safety_check",
            "elements": [
                {"type": "text", "name": "inspector_name", "title": "Inspector Name", "isRequired": true},
                {"type": "text", "name": "location", "title": "Location", "isRequired": true},
                {"type": "text", "name": "date", "title": "Inspection Date", "inputType": "date", "isRequired": true},
                {"type": "radiogroup", "name": "fire_safety", "title": "Fire safety equipment accessible?", "choices": ["Yes", "No", "N/A"], "isRequired": true},
                {"type": "radiogroup", "name": "emergency_exits", "title": "Emergency exits clear?", "choices": ["Yes", "No"], "isRequired": true},
                {"type": "comment", "name": "notes", "title": "Additional Notes", "rows": 4}
            ]
        }]
    }', 1, @AdminId),
    
    ('Equipment Maintenance', 'Standard equipment maintenance checklist', 'maintenance',
    '{
        "title": "Equipment Maintenance Log",
        "pages": [{
            "name": "maintenance",
            "elements": [
                {"type": "text", "name": "technician_name", "title": "Technician Name", "isRequired": true},
                {"type": "text", "name": "equipment_id", "title": "Equipment ID", "isRequired": true},
                {"type": "dropdown", "name": "equipment_type", "title": "Equipment Type", "choices": ["Mechanical", "Electrical", "Electronic", "Hydraulic"], "isRequired": true},
                {"type": "radiogroup", "name": "condition", "title": "Overall Condition", "choices": ["Excellent", "Good", "Fair", "Poor"], "isRequired": true},
                {"type": "rating", "name": "performance", "title": "Performance Rating", "rateMin": 1, "rateMax": 5},
                {"type": "comment", "name": "maintenance_notes", "title": "Maintenance Notes", "rows": 4}
            ]
        }]
    }', 1, @AdminId),
    
    ('Quality Control Check', 'Standard quality control inspection', 'quality',
    '{
        "title": "Quality Control Inspection",
        "pages": [{
            "name": "quality_check",
            "elements": [
                {"type": "text", "name": "inspector_name", "title": "Inspector Name", "isRequired": true},
                {"type": "text", "name": "batch_number", "title": "Batch Number", "isRequired": true},
                {"type": "text", "name": "inspection_date", "title": "Inspection Date", "inputType": "date", "isRequired": true},
                {"type": "radiogroup", "name": "visual_check", "title": "Visual Inspection", "choices": ["Pass", "Fail"], "isRequired": true},
                {"type": "radiogroup", "name": "dimensional_check", "title": "Dimensional Check", "choices": ["Pass", "Fail", "N/A"], "isRequired": true},
                {"type": "radiogroup", "name": "overall_result", "title": "Overall Result", "choices": ["Accept", "Reject", "Rework"], "isRequired": true},
                {"type": "comment", "name": "defects", "title": "Defects Found", "rows": 4}
            ]
        }]
    }', 1, @AdminId);
    
    PRINT 'Sample form templates inserted';
END
GO

PRINT '==============================================';
PRINT 'Checksheet Tables and Views Created Successfully!';
PRINT '==============================================';
PRINT 'New Tables:';
PRINT '- checksheet_sessions';
PRINT '- checksheet_responses'; 
PRINT '- checksheet_attachments';
PRINT '- form_templates';
PRINT '';
PRINT 'New Views:';
PRINT '- vw_forms_complete';
PRINT '- vw_user_activity_summary';
PRINT '- vw_department_statistics';
PRINT '- vw_checksheet_sessions_complete';
PRINT '';
PRINT 'New Stored Procedures:';
PRINT '- sp_StartChecksheetSession';
PRINT '- sp_SaveChecksheetResponse';
PRINT '- sp_CompleteChecksheetSession';
PRINT '';
PRINT 'Sample form templates added for Safety, Maintenance, and Quality Control';
PRINT '==============================================';