# FieldReporter Database Setup

## Overview
This directory contains the SQL Server database schema and setup scripts for the FieldReporter application.

## Files
- `create_tables.sql` - Main database schema creation script
- `sample_data.sql` - Sample data for testing (optional)
- `stored_procedures.sql` - Additional stored procedures
- `views.sql` - Database views for reporting

## Setup Instructions

### 1. Prerequisites
- SQL Server 2016 or later
- SQL Server Management Studio (SSMS) or Azure Data Studio
- Appropriate permissions to create databases

### 2. Database Creation
1. Open SQL Server Management Studio
2. Connect to your SQL Server instance
3. Open `create_tables.sql`
4. Execute the script (F5)

### 3. Verify Installation
After running the script, verify that:
- Database `FieldReporter` is created
- All 10 tables are present
- Default admin user is created
- Sample departments are inserted

### 4. Default Login
- **Email**: <EMAIL>
- **Password**: admin123 (change this immediately in production!)

## Database Schema

### Core Tables

#### users
Stores user accounts and authentication information
- `id` - Unique identifier
- `username` - User login name
- `email` - User email address
- `password_hash` - Encrypted password
- `role` - User role (admin, submit, approval, final_approval)
- `general_department`, `next_department`, `direct_department` - Department hierarchy
- `is_active` - Account status
- `created_at`, `updated_at` - Timestamps

#### departments
Department hierarchy structure
- `id` - Unique identifier
- `name` - Department name
- `type` - Department level (general, next, direct)
- `parent_id` - Parent department reference
- `is_active` - Department status

#### folders
File system organization
- `id` - Unique identifier
- `name` - Folder name
- `description` - Folder description
- `parent_id` - Parent folder reference
- `general_department`, `next_department`, `direct_department` - Access control
- `created_by` - Creator user ID

#### forms
Form definitions and templates
- `id` - Unique identifier
- `name` - Form name
- `description` - Form description
- `folder_id` - Parent folder
- `form_type` - Type (survey, excel, custom)
- `json_schema` - SurveyJS form definition
- `excel_structure` - Excel form structure
- `general_department`, `next_department`, `direct_department` - Access control
- `created_by` - Creator user ID
- `version` - Form version number

#### form_submissions
Form submission data and workflow
- `id` - Unique identifier
- `form_id` - Reference to form
- `user_id` - Submitter user ID
- `submission_data` - JSON form data
- `status` - Workflow status (draft, submitted, approved, rejected, final_approved)
- `approval_notes` - Approval comments
- `approved_by`, `final_approved_by` - Approver user IDs
- `latitude`, `longitude` - GPS coordinates
- `location_name` - Location description
- `time_spent` - Time to complete form

#### submission_attachments
File attachments for submissions
- `id` - Unique identifier
- `submission_id` - Reference to submission
- `file_name` - Original filename
- `file_path` - Storage path
- `file_size` - File size in bytes
- `file_type` - File extension
- `mime_type` - MIME type

### System Tables

#### audit_logs
System audit trail
- `id` - Unique identifier
- `user_id` - User who performed action
- `action` - Action type (INSERT, UPDATE, DELETE)
- `table_name` - Affected table
- `record_id` - Affected record ID
- `old_values`, `new_values` - JSON change data
- `ip_address`, `user_agent` - Client information

#### user_sessions
User session management
- `id` - Unique identifier
- `user_id` - User reference
- `session_token` - Session token
- `refresh_token` - Refresh token
- `ip_address`, `user_agent` - Client information
- `expires_at` - Session expiration
- `is_active` - Session status

#### system_settings
Application configuration
- `id` - Unique identifier
- `setting_key` - Setting name
- `setting_value` - Setting value
- `setting_type` - Data type (string, number, boolean, json)
- `is_public` - Whether accessible by frontend

#### notifications
User notifications
- `id` - Unique identifier
- `user_id` - Target user
- `title` - Notification title
- `message` - Notification content
- `type` - Notification type (info, success, warning, error)
- `related_id`, `related_type` - Related record reference
- `is_read` - Read status

## Stored Procedures

### sp_GetUserPermissions
Returns user permissions based on role
```sql
EXEC sp_GetUserPermissions @UserId = 'user-guid-here'
```

### sp_GetAccessibleForms
Returns forms accessible to user based on department hierarchy
```sql
EXEC sp_GetAccessibleForms @UserId = 'user-guid-here'
```

## Security Features

### Role-Based Access Control
- **admin**: Full system access
- **final_approval**: Department-level oversight
- **approval**: First-level approval
- **submit**: Form creation and submission

### Department Hierarchy
Three-level department structure:
1. **General Department** - Top level (e.g., "Tổng Công Ty")
2. **Next Department** - Division level (e.g., "Phòng IT")
3. **Direct Department** - Team level (e.g., "Team Development")

### Audit Logging
Automatic tracking of all data changes with:
- User identification
- Timestamp
- Before/after values
- Client information

## Performance Considerations

### Indexes
All tables include appropriate indexes for:
- Primary keys
- Foreign keys
- Frequently queried columns
- Department hierarchy lookups

### Partitioning (Future)
Consider partitioning large tables by:
- `form_submissions` by date
- `audit_logs` by date
- `notifications` by user

## Backup Strategy

### Recommended Schedule
- **Full backup**: Weekly
- **Differential backup**: Daily
- **Transaction log backup**: Every 15 minutes

### Critical Tables
Priority backup order:
1. `form_submissions` - Business data
2. `forms` - Form definitions
3. `users` - User accounts
4. `audit_logs` - Compliance data

## Maintenance

### Regular Tasks
- Update statistics weekly
- Rebuild indexes monthly
- Archive old audit logs quarterly
- Clean expired sessions daily

### Monitoring
Monitor these metrics:
- Database size growth
- Query performance
- Failed login attempts
- Session count

## Connection String Example

```
Server=your-server;Database=FieldReporter;Trusted_Connection=true;
```

Or with SQL authentication:
```
Server=your-server;Database=FieldReporter;User Id=your-user;Password=your-password;
```

## Troubleshooting

### Common Issues
1. **Permission denied**: Ensure user has db_owner role
2. **Timeout errors**: Check network connectivity
3. **Lock timeouts**: Review concurrent operations
4. **Space issues**: Monitor database file growth

### Support
For database issues, check:
1. SQL Server error logs
2. Windows event logs
3. Application logs
4. Network connectivity