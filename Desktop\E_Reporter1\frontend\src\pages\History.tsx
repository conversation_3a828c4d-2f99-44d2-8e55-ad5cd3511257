import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  History as HistoryIcon, 
  Search, 
  Filter, 
  Calendar,
  User,
  FileText,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Download
} from 'lucide-react';

interface ActivityLog {
  id: string;
  timestamp: string;
  user: string;
  action: string;
  target: string;
  targetType: 'form' | 'submission' | 'user' | 'folder' | 'system';
  status: 'success' | 'error' | 'warning' | 'info';
  details: string;
  ipAddress?: string;
  userAgent?: string;
}

export function History() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterUser, setFilterUser] = useState('all');
  const [dateRange, setDateRange] = useState('today');

  // Mock data - replace with actual API calls
  const activities: ActivityLog[] = [
    {
      id: '1',
      timestamp: '2024-01-20 14:30:25',
      user: 'admin',
      action: 'LOGIN',
      target: 'System',
      targetType: 'system',
      status: 'success',
      details: 'User logged in successfully',
      ipAddress: '*************',
      userAgent: 'Chrome/120.0.0.0'
    },
    {
      id: '2',
      timestamp: '2024-01-20 14:25:15',
      user: '<EMAIL>',
      action: 'CREATE_FORM',
      target: 'Daily Safety Check',
      targetType: 'form',
      status: 'success',
      details: 'Created new safety inspection form',
      ipAddress: '*************'
    },
    {
      id: '3',
      timestamp: '2024-01-20 14:20:10',
      user: '<EMAIL>',
      action: 'SUBMIT_FORM',
      target: 'Quality Control Inspection',
      targetType: 'submission',
      status: 'success',
      details: 'Submitted quality control form for batch QC-2024-001',
      ipAddress: '*************'
    },
    {
      id: '4',
      timestamp: '2024-01-20 14:15:05',
      user: '<EMAIL>',
      action: 'APPROVE_SUBMISSION',
      target: 'Equipment Maintenance Log',
      targetType: 'submission',
      status: 'success',
      details: 'Approved maintenance log submission',
      ipAddress: '*************'
    },
    {
      id: '5',
      timestamp: '2024-01-20 14:10:30',
      user: '<EMAIL>',
      action: 'UPDATE_FORM',
      target: 'Server Maintenance Form',
      targetType: 'form',
      status: 'success',
      details: 'Updated form fields and validation rules',
      ipAddress: '*************'
    },
    {
      id: '6',
      timestamp: '2024-01-20 14:05:20',
      user: '<EMAIL>',
      action: 'CREATE_USER',
      target: '<EMAIL>',
      targetType: 'user',
      status: 'success',
      details: 'Created new user account with submit role',
      ipAddress: '*************'
    },
    {
      id: '7',
      timestamp: '2024-01-20 14:00:15',
      user: '<EMAIL>',
      action: 'DELETE_SUBMISSION',
      target: 'Monthly Report Draft',
      targetType: 'submission',
      status: 'warning',
      details: 'Deleted draft submission',
      ipAddress: '*************'
    },
    {
      id: '8',
      timestamp: '2024-01-20 13:55:10',
      user: 'system',
      action: 'BACKUP_DATABASE',
      target: 'Database',
      targetType: 'system',
      status: 'success',
      details: 'Automated database backup completed',
      ipAddress: 'localhost'
    },
    {
      id: '9',
      timestamp: '2024-01-20 13:50:05',
      user: '<EMAIL>',
      action: 'LOGIN_FAILED',
      target: 'System',
      targetType: 'system',
      status: 'error',
      details: 'Failed login attempt - invalid password',
      ipAddress: '*************'
    },
    {
      id: '10',
      timestamp: '2024-01-20 13:45:00',
      user: 'admin',
      action: 'UPDATE_SETTINGS',
      target: 'System Settings',
      targetType: 'system',
      status: 'success',
      details: 'Updated application configuration',
      ipAddress: '*************'
    }
  ];

  const actionTypes = [
    { value: 'all', label: 'All Actions' },
    { value: 'LOGIN', label: 'Login' },
    { value: 'LOGOUT', label: 'Logout' },
    { value: 'CREATE_FORM', label: 'Create Form' },
    { value: 'UPDATE_FORM', label: 'Update Form' },
    { value: 'DELETE_FORM', label: 'Delete Form' },
    { value: 'SUBMIT_FORM', label: 'Submit Form' },
    { value: 'APPROVE_SUBMISSION', label: 'Approve Submission' },
    { value: 'REJECT_SUBMISSION', label: 'Reject Submission' },
    { value: 'CREATE_USER', label: 'Create User' },
    { value: 'UPDATE_USER', label: 'Update User' },
    { value: 'DELETE_USER', label: 'Delete User' }
  ];

  const statusTypes = [
    { value: 'all', label: 'All Status' },
    { value: 'success', label: 'Success' },
    { value: 'error', label: 'Error' },
    { value: 'warning', label: 'Warning' },
    { value: 'info', label: 'Info' }
  ];

  const users = [
    { value: 'all', label: 'All Users' },
    { value: 'admin', label: 'Admin' },
    { value: 'system', label: 'System' },
    { value: '<EMAIL>', label: 'Dev1' },
    { value: '<EMAIL>', label: 'QC1' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Clock className="w-4 h-4 text-blue-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'info':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getActionIcon = (action: string) => {
    if (action.includes('FORM')) return <FileText className="w-4 h-4" />;
    if (action.includes('USER')) return <User className="w-4 h-4" />;
    if (action.includes('LOGIN') || action.includes('LOGOUT')) return <HistoryIcon className="w-4 h-4" />;
    return <Eye className="w-4 h-4" />;
  };

  const filteredActivities = activities.filter(activity => {
    const matchesSearch = activity.target.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.user.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAction = filterAction === 'all' || activity.action === filterAction;
    const matchesStatus = filterStatus === 'all' || activity.status === filterStatus;
    const matchesUser = filterUser === 'all' || activity.user === filterUser;
    
    return matchesSearch && matchesAction && matchesStatus && matchesUser;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Activity History</h1>
          <p className="text-gray-600 mt-1">Track all activities and changes in the system</p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4 mr-2" />
            Export Log
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search activities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Action Filter */}
          <select
            value={filterAction}
            onChange={(e) => setFilterAction(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {actionTypes.map(action => (
              <option key={action.value} value={action.value}>
                {action.label}
              </option>
            ))}
          </select>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {statusTypes.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          {/* User Filter */}
          <select
            value={filterUser}
            onChange={(e) => setFilterUser(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {users.map(user => (
              <option key={user.value} value={user.value}>
                {user.label}
              </option>
            ))}
          </select>

          {/* Date Range */}
          <div className="flex items-center space-x-2">
            <Calendar className="w-4 h-4 text-gray-500" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="last7days">Last 7 days</option>
              <option value="last30days">Last 30 days</option>
              <option value="last90days">Last 90 days</option>
            </select>
          </div>
        </div>
      </div>

      {/* Activity Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Activities</p>
              <p className="text-2xl font-bold text-gray-900">{activities.length}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <HistoryIcon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Successful</p>
              <p className="text-2xl font-bold text-gray-900">
                {activities.filter(a => a.status === 'success').length}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Errors</p>
              <p className="text-2xl font-bold text-gray-900">
                {activities.filter(a => a.status === 'error').length}
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Warnings</p>
              <p className="text-2xl font-bold text-gray-900">
                {activities.filter(a => a.status === 'warning').length}
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <AlertCircle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Activity Log Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Activity Log ({filteredActivities.length} entries)
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP Address
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredActivities.map((activity) => (
                <tr key={activity.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {activity.timestamp}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="w-4 h-4 text-gray-400 mr-2" />
                      <span className="text-sm font-medium text-gray-900">
                        {activity.user}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getActionIcon(activity.action)}
                      <span className="ml-2 text-sm text-gray-900">
                        {activity.action.replace('_', ' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{activity.target}</div>
                    <div className="text-xs text-gray-500 capitalize">{activity.targetType}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(activity.status)}
                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                        {activity.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600 max-w-xs truncate">
                    {activity.details}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {activity.ipAddress}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredActivities.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <HistoryIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No activities found</h3>
          <p className="text-gray-600">
            {searchTerm || filterAction !== 'all' || filterStatus !== 'all' || filterUser !== 'all'
              ? 'Try adjusting your search or filters'
              : 'No activities have been recorded yet'
            }
          </p>
        </div>
      )}
    </div>
  );
}