{"name": "fieldreporter-backend", "version": "1.0.0", "description": "Backend API server for FieldReporter application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "node src/scripts/migrate.js"}, "keywords": ["fieldreporter", "forms", "api", "backend"], "author": "FieldReporter Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mssql": "^10.0.1", "multer": "^1.4.5-lts.1", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "express-validator": "^7.0.1", "cookie-parser": "^1.4.6"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}