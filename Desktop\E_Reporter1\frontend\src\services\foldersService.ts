import { apiClient } from '../config/api';

export interface Folder {
  id: string;
  name: string;
  description?: string;
  parent_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  subfolder_count: number;
  form_count: number;
}

export interface GetFoldersResponse {
  success: boolean;
  data: {
    folders: Folder[];
  }
}

export interface GetFolderPathResponse {
  success: boolean;
  data: {
    path: Array<{
      id: string | null;
      name: string;
    }>;
  }
}

export const foldersService = {
  async getFolders(parent_id?: string): Promise<GetFoldersResponse> {
    const response = await apiClient.get('/folders', {
      params: { parent_id }
    });
    return response.data;
  },

  async getFolderById(id: string) {
    const response = await apiClient.get(`/folders/${id}`);
    return response.data;
  },

  async getFolderPath(id: string): Promise<GetFolderPathResponse> {
    const response = await apiClient.get(`/folders/${id}/path`);
    return response.data;
  },

  async createFolder(folderData: {
    name: string;
    description?: string;
    parent_id?: string;
  }) {
    const response = await apiClient.post('/folders', folderData);
    return response.data;
  },

  async updateFolder(id: string, folderData: {
    name?: string;
    description?: string;
  }) {
    const response = await apiClient.put(`/folders/${id}`, folderData);
    return response.data;
  },

  async deleteFolder(id: string) {
    const response = await apiClient.delete(`/folders/${id}`);
    return response.data;
  }
};
