-- FieldReporter Sample Data
-- This script inserts sample data for testing and development

USE FieldReporter;
GO

-- =============================================
-- SAMPLE USERS
-- =============================================

-- Clear existing sample data (except admin)
DELETE FROM form_submissions WHERE user_id != (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM forms WHERE created_by != (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM folders WHERE created_by != (SELECT id FROM users WHERE email = '<EMAIL>');
DELETE FROM users WHERE email != '<EMAIL>';

-- Insert sample users
DECLARE @adminId UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');

INSERT INTO users (username, email, password_hash, role, general_department, next_department, direct_department) VALUES
('manager_it', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'final_approval', 'Tổng Công Ty', 'Phòng IT', 'Team Development'),
('approval_it', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'approval', 'Tổng Công Ty', 'Phòng IT', 'Team Development'),
('user_dev1', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'submit', 'Tổng Công Ty', 'Phòng IT', 'Team Development'),
('user_dev2', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'submit', 'Tổng Công Ty', 'Phòng IT', 'Team Development'),

('manager_prod', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'final_approval', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team QC'),
('approval_qc', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'approval', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team QC'),
('user_qc1', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'submit', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team QC'),
('user_qc2', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'submit', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team QC'),

('user_prod1', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'submit', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team Production'),
('user_prod2', '<EMAIL>', '$2b$10$rOzJqQZQQQQQQQQQQQQQQu', 'submit', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team Production');

-- =============================================
-- SAMPLE FOLDERS
-- =============================================

DECLARE @dev1Id UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');
DECLARE @qc1Id UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');
DECLARE @prod1Id UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');

-- IT Department Folders
DECLARE @itSafetyFolder UNIQUEIDENTIFIER = NEWID();
DECLARE @itMaintenanceFolder UNIQUEIDENTIFIER = NEWID();

INSERT INTO folders (id, name, description, general_department, next_department, direct_department, created_by) VALUES
(@itSafetyFolder, 'IT Safety Forms', 'Safety inspection forms for IT department', 'Tổng Công Ty', 'Phòng IT', 'Team Development', @dev1Id),
(@itMaintenanceFolder, 'IT Maintenance', 'Equipment maintenance forms for IT', 'Tổng Công Ty', 'Phòng IT', 'Team Development', @dev1Id),
(NEWID(), 'IT Projects', 'Project management forms', 'Tổng Công Ty', 'Phòng IT', 'Team Development', @dev1Id);

-- Production Department Folders
DECLARE @prodQualityFolder UNIQUEIDENTIFIER = NEWID();
DECLARE @prodSafetyFolder UNIQUEIDENTIFIER = NEWID();

INSERT INTO folders (id, name, description, general_department, next_department, direct_department, created_by) VALUES
(@prodQualityFolder, 'Quality Control', 'Quality control inspection forms', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team QC', @qc1Id),
(@prodSafetyFolder, 'Production Safety', 'Safety forms for production floor', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team Production', @prod1Id),
(NEWID(), 'Equipment Logs', 'Production equipment maintenance logs', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team Production', @prod1Id);

-- =============================================
-- SAMPLE FORMS
-- =============================================

-- IT Safety Form
DECLARE @itSafetyForm UNIQUEIDENTIFIER = NEWID();
INSERT INTO forms (id, name, description, folder_id, form_type, json_schema, general_department, next_department, direct_department, created_by) VALUES
(@itSafetyForm, 'Daily IT Safety Check', 'Daily safety inspection for IT equipment and workspace', @itSafetyFolder, 'survey', 
'{
  "title": "Daily IT Safety Check",
  "description": "Complete this form for daily IT safety inspections",
  "pages": [{
    "name": "safety_check",
    "elements": [
      {
        "type": "text",
        "name": "inspector_name",
        "title": "Inspector Name",
        "isRequired": true
      },
      {
        "type": "text",
        "name": "location",
        "title": "Location",
        "isRequired": true
      },
      {
        "type": "text",
        "name": "date",
        "title": "Inspection Date",
        "inputType": "date",
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "fire_extinguisher",
        "title": "Fire extinguisher accessible and charged?",
        "choices": ["Yes", "No", "N/A"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "emergency_exits",
        "title": "Emergency exits clear and marked?",
        "choices": ["Yes", "No"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "electrical_safety",
        "title": "Electrical panels accessible and labeled?",
        "choices": ["Yes", "No"],
        "isRequired": true
      },
      {
        "type": "comment",
        "name": "notes",
        "title": "Additional Notes",
        "rows": 4
      }
    ]
  }]
}', 'Tổng Công Ty', 'Phòng IT', 'Team Development', @dev1Id);

-- Equipment Maintenance Form
DECLARE @equipmentForm UNIQUEIDENTIFIER = NEWID();
INSERT INTO forms (id, name, description, folder_id, form_type, json_schema, general_department, next_department, direct_department, created_by) VALUES
(@equipmentForm, 'Server Maintenance Log', 'Weekly server maintenance checklist', @itMaintenanceFolder, 'survey',
'{
  "title": "Server Maintenance Log",
  "description": "Weekly server maintenance and monitoring",
  "pages": [{
    "name": "maintenance",
    "elements": [
      {
        "type": "text",
        "name": "technician_name",
        "title": "Technician Name",
        "isRequired": true
      },
      {
        "type": "text",
        "name": "server_id",
        "title": "Server ID",
        "isRequired": true
      },
      {
        "type": "dropdown",
        "name": "server_type",
        "title": "Server Type",
        "choices": ["Web Server", "Database Server", "File Server", "Mail Server", "Other"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "system_status",
        "title": "System Status",
        "choices": ["Online", "Offline", "Maintenance Mode"],
        "isRequired": true
      },
      {
        "type": "rating",
        "name": "performance_rating",
        "title": "Performance Rating",
        "rateMin": 1,
        "rateMax": 5,
        "rateStep": 1,
        "minRateDescription": "Poor",
        "maxRateDescription": "Excellent"
      },
      {
        "type": "comment",
        "name": "maintenance_notes",
        "title": "Maintenance Notes",
        "rows": 4
      }
    ]
  }]
}', 'Tổng Công Ty', 'Phòng IT', 'Team Development', @dev1Id);

-- Quality Control Form
DECLARE @qualityForm UNIQUEIDENTIFIER = NEWID();
INSERT INTO forms (id, name, description, folder_id, form_type, json_schema, general_department, next_department, direct_department, created_by) VALUES
(@qualityForm, 'Product Quality Inspection', 'Quality control inspection for manufactured products', @prodQualityFolder, 'survey',
'{
  "title": "Product Quality Inspection",
  "description": "Quality control inspection checklist",
  "pages": [{
    "name": "quality_check",
    "elements": [
      {
        "type": "text",
        "name": "inspector_name",
        "title": "Inspector Name",
        "isRequired": true
      },
      {
        "type": "text",
        "name": "product_batch",
        "title": "Product Batch Number",
        "isRequired": true
      },
      {
        "type": "text",
        "name": "inspection_date",
        "title": "Inspection Date",
        "inputType": "date",
        "isRequired": true
      },
      {
        "type": "dropdown",
        "name": "product_type",
        "title": "Product Type",
        "choices": ["Type A", "Type B", "Type C", "Type D"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "visual_inspection",
        "title": "Visual Inspection Result",
        "choices": ["Pass", "Fail", "Conditional"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "dimensional_check",
        "title": "Dimensional Check",
        "choices": ["Pass", "Fail", "N/A"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "functional_test",
        "title": "Functional Test",
        "choices": ["Pass", "Fail", "N/A"],
        "isRequired": true
      },
      {
        "type": "radiogroup",
        "name": "overall_result",
        "title": "Overall Result",
        "choices": ["Accept", "Reject", "Rework Required"],
        "isRequired": true
      },
      {
        "type": "comment",
        "name": "defects_notes",
        "title": "Defects and Notes",
        "rows": 4
      }
    ]
  }]
}', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team QC', @qc1Id);

-- Excel-based Form Example
DECLARE @excelForm UNIQUEIDENTIFIER = NEWID();
INSERT INTO forms (id, name, description, folder_id, form_type, excel_structure, general_department, next_department, direct_department, created_by) VALUES
(@excelForm, 'Monthly Report Template', 'Excel-based monthly reporting template', @prodSafetyFolder, 'excel',
'{
  "cells": [
    {"address": "A1", "value": "Monthly Safety Report", "type": "static", "row": 0, "col": 0, "style": {"fontWeight": "bold", "fontSize": "16px"}},
    {"address": "A3", "value": "Report Month:", "type": "static", "row": 2, "col": 0},
    {"address": "B3", "value": "", "type": "input", "row": 2, "col": 1, "cellType": "date"},
    {"address": "A4", "value": "Department:", "type": "static", "row": 3, "col": 0},
    {"address": "B4", "value": "", "type": "input", "row": 3, "col": 1, "cellType": "text"},
    {"address": "A5", "value": "Reporter:", "type": "static", "row": 4, "col": 0},
    {"address": "B5", "value": "", "type": "input", "row": 4, "col": 1, "cellType": "text"},
    {"address": "A7", "value": "Safety Incidents:", "type": "static", "row": 6, "col": 0},
    {"address": "B7", "value": "", "type": "input", "row": 6, "col": 1, "cellType": "number"},
    {"address": "A8", "value": "Near Misses:", "type": "static", "row": 7, "col": 0},
    {"address": "B8", "value": "", "type": "input", "row": 7, "col": 1, "cellType": "number"},
    {"address": "A9", "value": "Total Events:", "type": "formula", "row": 8, "col": 0, "formula": "B7+B8"}
  ],
  "merges": [
    {"start": {"row": 0, "col": 0}, "end": {"row": 0, "col": 3}}
  ],
  "dimensions": {"rows": 15, "cols": 5}
}', 'Tổng Công Ty', 'Phòng Sản Xuất', 'Team Production', @prod1Id);

-- =============================================
-- SAMPLE FORM SUBMISSIONS
-- =============================================

DECLARE @dev2Id UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');
DECLARE @qc2Id UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');
DECLARE @approvalItId UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');
DECLARE @approvalQcId UNIQUEIDENTIFIER = (SELECT id FROM users WHERE email = '<EMAIL>');

-- IT Safety Submissions
INSERT INTO form_submissions (form_id, user_id, submission_data, status, latitude, longitude, location_name, time_spent) VALUES
(@itSafetyForm, @dev1Id, 
'{
  "inspector_name": "John Smith",
  "location": "Server Room A",
  "date": "2024-01-15",
  "fire_extinguisher": "Yes",
  "emergency_exits": "Yes", 
  "electrical_safety": "Yes",
  "notes": "All safety checks passed. No issues found."
}', 'final_approved', 10.7756, 106.7019, 'Server Room A - Building 1', 180),

(@itSafetyForm, @dev2Id,
'{
  "inspector_name": "Jane Doe",
  "location": "Network Closet B",
  "date": "2024-01-14",
  "fire_extinguisher": "Yes",
  "emergency_exits": "No",
  "electrical_safety": "Yes", 
  "notes": "Emergency exit blocked by equipment. Reported to facilities."
}', 'approved', 10.7756, 106.7019, 'Network Closet B - Building 2', 240);

-- Equipment Maintenance Submissions
INSERT INTO form_submissions (form_id, user_id, submission_data, status, time_spent) VALUES
(@equipmentForm, @dev1Id,
'{
  "technician_name": "Mike Johnson",
  "server_id": "SRV-001",
  "server_type": "Database Server",
  "system_status": "Online",
  "performance_rating": 4,
  "maintenance_notes": "Performed routine maintenance. Updated OS patches. System running optimally."
}', 'submitted', 300),

(@equipmentForm, @dev2Id,
'{
  "technician_name": "Sarah Wilson", 
  "server_id": "SRV-002",
  "server_type": "Web Server",
  "system_status": "Maintenance Mode",
  "performance_rating": 3,
  "maintenance_notes": "Disk space running low. Scheduled for upgrade next week."
}', 'draft', 420);

-- Quality Control Submissions
INSERT INTO form_submissions (form_id, user_id, submission_data, status, approved_by, approved_at, time_spent) VALUES
(@qualityForm, @qc1Id,
'{
  "inspector_name": "David Chen",
  "product_batch": "BATCH-2024-001",
  "inspection_date": "2024-01-15",
  "product_type": "Type A",
  "visual_inspection": "Pass",
  "dimensional_check": "Pass",
  "functional_test": "Pass",
  "overall_result": "Accept",
  "defects_notes": "No defects found. Product meets all quality standards."
}', 'approved', @approvalQcId, DATEADD(hour, -2, GETDATE()), 360),

(@qualityForm, @qc2Id,
'{
  "inspector_name": "Lisa Wang",
  "product_batch": "BATCH-2024-002", 
  "inspection_date": "2024-01-14",
  "product_type": "Type B",
  "visual_inspection": "Fail",
  "dimensional_check": "Pass",
  "functional_test": "Conditional",
  "overall_result": "Rework Required",
  "defects_notes": "Surface scratches detected. Minor functional issues. Requires rework before acceptance."
}', 'submitted', 450);

-- =============================================
-- SAMPLE NOTIFICATIONS
-- =============================================

INSERT INTO notifications (user_id, title, message, type, related_id, related_type) VALUES
(@approvalItId, 'New Form Submission', 'A new IT safety inspection form has been submitted and requires your approval.', 'info', @itSafetyForm, 'form_submission'),
(@approvalQcId, 'Quality Issue Detected', 'A quality control inspection has identified issues requiring attention.', 'warning', @qualityForm, 'form_submission'),
(@dev1Id, 'Form Approved', 'Your safety inspection form has been approved by the supervisor.', 'success', @itSafetyForm, 'form_submission'),
(@qc2Id, 'Rework Required', 'Your quality inspection indicates rework is required for the submitted batch.', 'warning', @qualityForm, 'form_submission');

-- =============================================
-- UPDATE APPROVAL RECORDS
-- =============================================

-- Update some submissions with approval information
UPDATE form_submissions 
SET approved_by = @approvalItId, approved_at = DATEADD(hour, -1, GETDATE())
WHERE form_id = @itSafetyForm AND user_id = @dev1Id;

UPDATE form_submissions
SET approved_by = @approvalQcId, approved_at = DATEADD(hour, -3, GETDATE())  
WHERE form_id = @qualityForm AND user_id = @qc1Id;

PRINT '==============================================';
PRINT 'Sample Data Inserted Successfully!';
PRINT '==============================================';
PRINT 'Sample Users Created:';
PRINT '- IT Manager: <EMAIL>';
PRINT '- IT Approval: <EMAIL>';
PRINT '- IT Users: <EMAIL>, <EMAIL>';
PRINT '- Production Manager: <EMAIL>';
PRINT '- QC Approval: <EMAIL>';
PRINT '- QC Users: <EMAIL>, <EMAIL>';
PRINT '- Production Users: <EMAIL>, <EMAIL>';
PRINT '';
PRINT 'Sample Forms Created:';
PRINT '- Daily IT Safety Check';
PRINT '- Server Maintenance Log';
PRINT '- Product Quality Inspection';
PRINT '- Monthly Report Template (Excel)';
PRINT '';
PRINT 'Sample Submissions: 6 submissions with various statuses';
PRINT 'Sample Notifications: 4 notifications for different scenarios';
PRINT '==============================================';
PRINT 'All passwords: admin123';
PRINT '==============================================';