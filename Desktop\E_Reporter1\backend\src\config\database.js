const sql = require('mssql');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

// Check if we should use mock database for testing
const USE_MOCK_DB = process.env.USE_MOCK_DB === 'true' || process.env.NODE_ENV === 'test';

if (USE_MOCK_DB) {
  console.log('🔧 Using Mock Database for testing');
  module.exports = require('./database-mock');
  return;
}

const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_DATABASE || 'FieldReporter',
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_SERVER_CERTIFICATE === 'true',
    enableArithAbort: true,
    requestTimeout: 30000,
    connectionTimeout: 30000,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

class Database {
  constructor() {
    this.pool = null;
    this.connected = false;
  }

  async connect() {
    try {
      if (this.pool) {
        await this.pool.close();
      }
      
      this.pool = await sql.connect(config);
      this.connected = true;
      console.log('✅ Connected to SQL Server database');
      
      // Test connection
      await this.pool.request().query('SELECT 1 as test');
      console.log('✅ Database connection test successful');
      
      return this.pool;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      this.connected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.pool) {
        await this.pool.close();
        this.pool = null;
        this.connected = false;
        console.log('✅ Disconnected from database');
      }
    } catch (error) {
      console.error('❌ Error disconnecting from database:', error.message);
    }
  }

  getPool() {
    if (!this.pool || !this.connected) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.pool;
  }

  async query(queryString, params = {}) {
    try {
      const pool = this.getPool();
      const request = pool.request();
      
      // Add parameters
      Object.keys(params).forEach(key => {
        request.input(key, params[key]);
      });
      
      const result = await request.query(queryString);
      return result;
    } catch (error) {
      console.error('❌ Database query error:', error.message);
      throw error;
    }
  }

  async execute(procedureName, params = {}) {
    try {
      const pool = this.getPool();
      const request = pool.request();
      
      // Add parameters
      Object.keys(params).forEach(key => {
        request.input(key, params[key]);
      });
      
      const result = await request.execute(procedureName);
      return result;
    } catch (error) {
      console.error('❌ Stored procedure execution error:', error.message);
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      const result = await this.query('SELECT GETDATE() as current_time, @@VERSION as version');
      return {
        status: 'healthy',
        timestamp: result.recordset[0].current_time,
        version: result.recordset[0].version,
        connected: this.connected
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        connected: false
      };
    }
  }
}

// Create singleton instance
const database = new Database();

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🔄 Gracefully shutting down database connection...');
  await database.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔄 Gracefully shutting down database connection...');
  await database.disconnect();
  process.exit(0);
});

module.exports = database;