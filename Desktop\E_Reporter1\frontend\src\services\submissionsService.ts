import { apiClient } from '../config/api';

export interface Submission {
  id: string;
  form_id: string;
  user_id: string;
  submission_data: any;
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'final_approved';
  approval_notes?: string;
  latitude?: number;
  longitude?: number;
  location_name?: string;
  time_spent?: number;
  created_at: string;
  updated_at: string;
  approved_at?: string;
  final_approved_at?: string;
  submitted_by: string;
  approved_by_name?: string;
  final_approved_by_name?: string;
  form_name: string;
  form_type: 'survey' | 'excel';
  attachments?: Array<{
    id: string;
    file_name: string;
    file_size: number;
    file_type: string;
    uploaded_at: string;
  }>;
}

export interface GetSubmissionsResponse {
  success: boolean;
  data: {
    submissions: Submission[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    }
  }
}

export interface SubmissionStats {
  total_submissions: number;
  draft_count: number;
  submitted_count: number;
  approved_count: number;
  rejected_count: number;
  final_approved_count: number;
  avg_time_spent: number;
}

export interface GetSubmissionStatsResponse {
  success: boolean;
  data: {
    statistics: SubmissionStats;
  }
}

export const submissionsService = {
  async getSubmissions(params?: {
    page?: number;
    limit?: number;
    form_id?: string;
    status?: string;
    start_date?: string;
    end_date?: string;
    user_id?: string;
  }): Promise<GetSubmissionsResponse> {
    const response = await apiClient.get('/submissions', { params });
    return response.data;
  },

  async getSubmissionById(id: string) {
    const response = await apiClient.get(`/submissions/${id}`);
    return response.data;
  },

  async getSubmissionStats(params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<GetSubmissionStatsResponse> {
    const response = await apiClient.get('/submissions/stats', { params });
    return response.data;
  },

  async createSubmission(submissionData: {
    form_id: string;
    submission_data: any;
    status?: string;
    latitude?: number;
    longitude?: number;
    location_name?: string;
    time_spent?: number;
  }) {
    const response = await apiClient.post('/submissions', submissionData);
    return response.data;
  },

  async updateSubmissionStatus(id: string, data: {
    status: string;
    approval_notes?: string;
  }) {
    const response = await apiClient.patch(`/submissions/${id}/status`, data);
    return response.data;
  },

  async deleteSubmission(id: string) {
    const response = await apiClient.delete(`/submissions/${id}`);
    return response.data;
  }
};
