import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  ArrowLeft, 
  Save, 
  Send, 
  Clock, 
  MapPin, 
  FileText,
  CheckCircle,
  AlertCircle,
  Camera,
  Paperclip,
  RotateCcw
} from 'lucide-react';

interface ChecksheetSession {
  id: string;
  form_id: string;
  form_name: string;
  session_name: string;
  status: 'in_progress' | 'completed' | 'paused';
  progress_percentage: number;
  total_questions: number;
  answered_questions: number;
  started_at: string;
  location_name?: string;
}

interface Question {
  id: string;
  type: 'text' | 'textarea' | 'radio' | 'checkbox' | 'dropdown' | 'rating' | 'date';
  title: string;
  isRequired: boolean;
  choices?: string[];
  placeholder?: string;
  rateMin?: number;
  rateMax?: number;
  rows?: number;
}

interface Response {
  question_id: string;
  response_value: string;
  is_answered: boolean;
}

export function PerformChecksheet() {
  const { formId } = useParams<{ formId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [session, setSession] = useState<ChecksheetSession | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [responses, setResponses] = useState<Record<string, Response>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showLocationPrompt, setShowLocationPrompt] = useState(true);
  const [location, setLocation] = useState<{ lat: number; lng: number; name: string } | null>(null);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const loadChecksheetSession = async () => {
      setIsLoading(true);
      
      // Mock session data
      const mockSession: ChecksheetSession = {
        id: 'session-1',
        form_id: formId || 'form-1',
        form_name: 'Daily Safety Inspection',
        session_name: `Safety Check - ${new Date().toLocaleDateString()}`,
        status: 'in_progress',
        progress_percentage: 0,
        total_questions: 6,
        answered_questions: 0,
        started_at: new Date().toISOString(),
        location_name: 'Building A - Floor 1'
      };

      // Mock questions
      const mockQuestions: Question[] = [
        {
          id: 'inspector_name',
          type: 'text',
          title: 'Inspector Name',
          isRequired: true,
          placeholder: 'Enter your name'
        },
        {
          id: 'location',
          type: 'text',
          title: 'Inspection Location',
          isRequired: true,
          placeholder: 'Enter location details'
        },
        {
          id: 'fire_safety',
          type: 'radio',
          title: 'Are fire safety equipment accessible and in good condition?',
          isRequired: true,
          choices: ['Yes', 'No', 'N/A']
        },
        {
          id: 'emergency_exits',
          type: 'radio',
          title: 'Are emergency exits clear and properly marked?',
          isRequired: true,
          choices: ['Yes', 'No']
        },
        {
          id: 'overall_rating',
          type: 'rating',
          title: 'Overall Safety Rating',
          isRequired: true,
          rateMin: 1,
          rateMax: 5
        },
        {
          id: 'notes',
          type: 'textarea',
          title: 'Additional Notes and Observations',
          isRequired: false,
          rows: 4,
          placeholder: 'Enter any additional observations or concerns...'
        }
      ];

      setSession(mockSession);
      setQuestions(mockQuestions);
      
      // Initialize responses
      const initialResponses: Record<string, Response> = {};
      mockQuestions.forEach(q => {
        initialResponses[q.id] = {
          question_id: q.id,
          response_value: '',
          is_answered: false
        };
      });
      setResponses(initialResponses);
      
      setIsLoading(false);
    };

    loadChecksheetSession();
  }, [formId]);

  // Get current location
  useEffect(() => {
    if (showLocationPrompt && navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            name: `Location: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`
          });
          setShowLocationPrompt(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          setShowLocationPrompt(false);
        }
      );
    }
  }, [showLocationPrompt]);

  const handleResponseChange = (questionId: string, value: string) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        response_value: value,
        is_answered: value.trim() !== ''
      }
    }));

    // Update session progress
    if (session) {
      const answeredCount = Object.values({
        ...responses,
        [questionId]: { ...responses[questionId], is_answered: value.trim() !== '' }
      }).filter(r => r.is_answered).length;
      
      setSession(prev => prev ? {
        ...prev,
        answered_questions: answeredCount,
        progress_percentage: (answeredCount / prev.total_questions) * 100
      } : null);
    }
  };

  const handleSaveResponse = async () => {
    setIsSaving(true);
    
    // Mock API call to save response
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setIsSaving(false);
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleCompleteChecksheet = async () => {
    setIsSaving(true);
    
    // Check if all required questions are answered
    const unansweredRequired = questions.filter(q => 
      q.isRequired && !responses[q.id]?.is_answered
    );

    if (unansweredRequired.length > 0) {
      alert(`Please answer all required questions: ${unansweredRequired.map(q => q.title).join(', ')}`);
      setIsSaving(false);
      return;
    }

    // Mock API call to complete checksheet
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSaving(false);
    navigate('/forms', { 
      state: { message: 'Checksheet completed successfully!' }
    });
  };

  const renderQuestion = (question: Question) => {
    const response = responses[question.id];
    
    switch (question.type) {
      case 'text':
        return (
          <input
            type="text"
            value={response?.response_value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            placeholder={question.placeholder}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        );

      case 'textarea':
        return (
          <textarea
            value={response?.response_value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            placeholder={question.placeholder}
            rows={question.rows || 4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        );

      case 'radio':
        return (
          <div className="space-y-3">
            {question.choices?.map((choice, index) => (
              <label key={index} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name={question.id}
                  value={choice}
                  checked={response?.response_value === choice}
                  onChange={(e) => handleResponseChange(question.id, e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="text-gray-700">{choice}</span>
              </label>
            ))}
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-3">
            {question.choices?.map((choice, index) => (
              <label key={index} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  value={choice}
                  checked={response?.response_value.includes(choice)}
                  onChange={(e) => {
                    const currentValues = response?.response_value ? response.response_value.split(',') : [];
                    const newValues = e.target.checked
                      ? [...currentValues, choice]
                      : currentValues.filter(v => v !== choice);
                    handleResponseChange(question.id, newValues.join(','));
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-gray-700">{choice}</span>
              </label>
            ))}
          </div>
        );

      case 'dropdown':
        return (
          <select
            value={response?.response_value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select an option...</option>
            {question.choices?.map((choice, index) => (
              <option key={index} value={choice}>{choice}</option>
            ))}
          </select>
        );

      case 'rating':
        return (
          <div className="flex items-center space-x-2">
            {Array.from({ length: (question.rateMax || 5) - (question.rateMin || 1) + 1 }, (_, i) => {
              const value = (question.rateMin || 1) + i;
              return (
                <button
                  key={value}
                  onClick={() => handleResponseChange(question.id, value.toString())}
                  className={`w-10 h-10 rounded-full border-2 font-medium transition-colors ${
                    response?.response_value === value.toString()
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-400'
                  }`}
                >
                  {value}
                </button>
              );
            })}
            <span className="text-sm text-gray-500 ml-4">
              {question.rateMin || 1} = Poor, {question.rateMax || 5} = Excellent
            </span>
          </div>
        );

      case 'date':
        return (
          <input
            type="date"
            value={response?.response_value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checksheet...</p>
        </div>
      </div>
    );
  }

  if (!session || questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Checksheet not found</h3>
          <p className="text-gray-600 mb-4">The requested checksheet could not be loaded.</p>
          <button
            onClick={() => navigate('/forms')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Back to Forms
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const currentResponse = responses[currentQuestion.id];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/forms')}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">{session.form_name}</h1>
                <p className="text-sm text-gray-600">{session.session_name}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {location && (
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="w-4 h-4 mr-1" />
                  <span>Location captured</span>
                </div>
              )}
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="w-4 h-4 mr-1" />
                <span>Started {new Date(session.started_at).toLocaleTimeString()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Question {currentQuestionIndex + 1} of {questions.length}
            </span>
            <span className="text-sm text-gray-600">
              {Math.round(session.progress_percentage)}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${session.progress_percentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          {/* Question */}
          <div className="mb-8">
            <div className="flex items-start space-x-3 mb-6">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="w-4 h-4 text-blue-600" />
                </div>
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  {currentQuestion.title}
                  {currentQuestion.isRequired && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </h2>
                {currentQuestion.isRequired && (
                  <p className="text-sm text-gray-600">This question is required</p>
                )}
              </div>
              {currentResponse?.is_answered && (
                <CheckCircle className="w-6 h-6 text-green-500" />
              )}
            </div>

            {/* Question Input */}
            <div className="mb-6">
              {renderQuestion(currentQuestion)}
            </div>

            {/* Attachments */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Camera className="w-4 h-4" />
                <span>Add Photo</span>
              </button>
              <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Paperclip className="w-4 h-4" />
                <span>Attach File</span>
              </button>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex space-x-3">
              <button
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>
              
              <button
                onClick={handleSaveResponse}
                disabled={isSaving}
                className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                {isSaving ? (
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                Save
              </button>
            </div>

            <div className="flex space-x-3">
              {currentQuestionIndex < questions.length - 1 ? (
                <button
                  onClick={handleNextQuestion}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Next
                </button>
              ) : (
                <button
                  onClick={handleCompleteChecksheet}
                  disabled={isSaving}
                  className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
                >
                  {isSaving ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  ) : (
                    <Send className="w-4 h-4 mr-2" />
                  )}
                  Complete Checksheet
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Question Overview */}
        <div className="mt-6 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Question Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {questions.map((question, index) => {
              const response = responses[question.id];
              return (
                <button
                  key={question.id}
                  onClick={() => setCurrentQuestionIndex(index)}
                  className={`p-3 text-left border rounded-lg transition-colors ${
                    index === currentQuestionIndex
                      ? 'border-blue-500 bg-blue-50'
                      : response?.is_answered
                      ? 'border-green-300 bg-green-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-900">
                      Q{index + 1}
                    </span>
                    {response?.is_answered && (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 truncate">
                    {question.title}
                  </p>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}