import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { TokenManager } from '../config/api';
import { authService } from '../services/authService';

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  general_department: string;
  next_department: string;
  direct_department: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    username: string;
    email: string;
    password: string;
    role?: string;
    general_department: string;
    next_department: string;
    direct_department: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initAuth = async () => {
      const token = TokenManager.getAccessToken();
      if (token) {
        try {
          const response = await authService.getProfile();
          setUser(response.data.user);
        } catch (error) {
          TokenManager.clearTokens();
        }
      }
      setIsLoading(false);
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setError(null);
      const response = await authService.login({ email, password });
      const { accessToken, refreshToken, user: userData } = response.data;
      
      TokenManager.setAccessToken(accessToken);
      TokenManager.setRefreshToken(refreshToken);
      setUser(userData);
    } catch (err: any) {
      const message = err.response?.data?.message || err.message || 'Login failed';
      setError(message);
      throw new Error(message);
    }
  };

  const register = async (userData: {
    username: string;
    email: string;
    password: string;
    role?: string;
    general_department: string;
    next_department: string;
    direct_department: string;
  }) => {
    try {
      setError(null);
      const response = await authService.register(userData);
      const { accessToken, refreshToken, user: responseUser } = response.data;
      TokenManager.setAccessToken(accessToken);
      TokenManager.setRefreshToken(refreshToken);
      setUser(responseUser);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Registration failed';
      setError(message);
      throw err;
    }
  };

  const logout = async () => {
    try {
      setError(null);
      await authService.logout();
      TokenManager.clearTokens();
      setUser(null);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Logout failed';
      setError(message);
      throw err;
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin has all permissions
    if (user.role === 'admin') return true;
    
    // Define role permissions
    const rolePermissions: Record<string, string[]> = {
      submit: [
        'create_folder',
        'create_form', 
        'perform_checksheet',
        'submit_checksheet',
        'save_final_approved_checksheet'
      ],
      approval: [
        'first_approval',
        'create_folder',
        'create_form',
        'save_final_approved_checksheet'
      ],
      final_approval: [
        'final_approval',
        'save_final_approved_checksheet',
        'view_all_next_department_content',
        'create_user'
      ]
    };
    
    const userPermissions = rolePermissions[user.role] || [];
    return userPermissions.includes(permission);
  };
  const clearError = () => {
    setError(null);
  };

  const value = {
    user,
    isLoading,
    error,
    login,
    register,
    logout,
    hasPermission,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}