# FieldReporter Backend API

## Overview
Node.js/Express backend API server for the FieldReporter application with SQL Server database integration.

## Features
- **RESTful API** with comprehensive endpoints
- **JWT Authentication** with refresh tokens
- **Role-based Access Control** (RBAC)
- **Department Hierarchy** access control
- **SQL Server Integration** with connection pooling
- **Input Validation** with <PERSON><PERSON> and express-validator
- **Rate Limiting** and security middleware
- **Comprehensive Error Handling**
- **Audit Logging** for all database changes
- **File Upload Support** (planned)

## Quick Start

### 1. Prerequisites
- Node.js 16+ 
- SQL Server 2016+
- Database created using `/database/create_tables.sql`

### 2. Installation
```bash
cd server
npm install
```

### 3. Environment Setup
```bash
cp .env.example .env
# Edit .env with your database credentials
```

### 4. Start Server
```bash
# Development
npm run dev

# Production
npm start
```

## API Endpoints

### Authentication
```
POST /api/auth/register     - Register new user
POST /api/auth/login        - User login
POST /api/auth/refresh-token - Refresh JWT token
POST /api/auth/logout       - User logout
GET  /api/auth/profile      - Get user profile
```

### Forms Management
```
GET    /api/forms           - Get all forms (paginated)
GET    /api/forms/:id       - Get single form
POST   /api/forms           - Create new form
PUT    /api/forms/:id       - Update form
DELETE /api/forms/:id       - Delete form
```

### Folders Management
```
GET    /api/folders         - Get all folders
GET    /api/folders/:id     - Get single folder
GET    /api/folders/:id/path - Get folder breadcrumb path
POST   /api/folders         - Create new folder
PUT    /api/folders/:id     - Update folder
DELETE /api/folders/:id     - Delete folder
```

### Form Submissions
```
GET    /api/submissions     - Get all submissions (paginated)
GET    /api/submissions/stats - Get submission statistics
GET    /api/submissions/:id - Get single submission
POST   /api/submissions     - Create new submission
PATCH  /api/submissions/:id/status - Update submission status
DELETE /api/submissions/:id - Delete submission
```

### System
```
GET /api/health            - Health check
```

## Authentication

### Login Request
```json
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Response
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "username": "user",
      "email": "<EMAIL>",
      "role": "submit",
      "general_department": "Tổng Công Ty",
      "next_department": "Phòng IT",
      "direct_department": "Team Development"
    },
    "accessToken": "jwt_token_here",
    "refreshToken": "refresh_token_here"
  }
}
```

### Using JWT Token
```javascript
// Add to request headers
Authorization: Bearer your_jwt_token_here
```

## Role-Based Permissions

### Roles & Permissions
- **admin**: Full system access
- **final_approval**: Department-level oversight + user creation
- **approval**: First-level approval within department
- **submit**: Form creation and submission

### Permission Matrix
| Permission | admin | final_approval | approval | submit |
|------------|-------|----------------|----------|--------|
| create_folder | ✅ | ❌ | ✅ | ✅ |
| create_form | ✅ | ❌ | ✅ | ✅ |
| perform_checksheet | ✅ | ❌ | ❌ | ✅ |
| submit_checksheet | ✅ | ❌ | ❌ | ✅ |
| first_approval | ✅ | ❌ | ✅ | ❌ |
| final_approval | ✅ | ✅ | ❌ | ❌ |
| create_user | ✅ | ✅ | ❌ | ❌ |
| view_all_next_department_content | ✅ | ✅ | ❌ | ❌ |

## Department Access Control

### 3-Level Hierarchy
1. **General Department** (e.g., "Tổng Công Ty")
2. **Next Department** (e.g., "Phòng IT") 
3. **Direct Department** (e.g., "Team Development")

### Access Rules
- **admin**: Access to all departments
- **final_approval**: Access to all content in their `next_department` level
- **approval/submit**: Access only to their exact department path

## API Examples

### Create Form
```json
POST /api/forms
Authorization: Bearer your_token

{
  "name": "Safety Inspection Form",
  "description": "Daily safety inspection checklist",
  "form_type": "survey",
  "folder_id": "folder-uuid-here",
  "json_schema": {
    "title": "Safety Inspection",
    "pages": [...]
  }
}
```

### Submit Form
```json
POST /api/submissions
Authorization: Bearer your_token

{
  "form_id": "form-uuid-here",
  "submission_data": {
    "inspector_name": "John Doe",
    "location": "Building A",
    "safety_check": "Pass"
  },
  "latitude": 10.7756,
  "longitude": 106.7019,
  "location_name": "Building A - Floor 1",
  "time_spent": 300
}
```

### Approve Submission
```json
PATCH /api/submissions/:id/status
Authorization: Bearer your_token

{
  "status": "approved",
  "approval_notes": "All safety checks passed"
}
```

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Valid email is required"
    }
  ]
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request / Validation Error
- `401` - Unauthorized
- `403` - Forbidden / Insufficient Permissions
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable (Database)

## Database Integration

### Connection Configuration
```javascript
// config/database.js
const config = {
  server: process.env.DB_SERVER,
  database: process.env.DB_DATABASE,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  options: {
    encrypt: true,
    trustServerCertificate: true
  }
};
```

### Query Examples
```javascript
// Simple query
const result = await database.query(
  'SELECT * FROM users WHERE email = @email',
  { email: '<EMAIL>' }
);

// Stored procedure
const result = await database.execute(
  'sp_GetUserPermissions',
  { UserId: 'user-uuid' }
);
```

## Security Features

### Rate Limiting
- 100 requests per 15 minutes per IP
- Configurable via environment variables

### Input Validation
```javascript
// Example validation middleware
const validateFormCreation = [
  body('name').trim().isLength({ min: 1, max: 255 }),
  body('form_type').isIn(['survey', 'excel', 'custom']),
  handleValidationErrors
];
```

### SQL Injection Prevention
- Parameterized queries only
- No dynamic SQL construction

### JWT Security
- Access tokens: 24 hours (configurable)
- Refresh tokens: 7 days (configurable)
- Secure token storage in database

## Development

### Project Structure
```
server/
├── src/
│   ├── config/          # Database and app configuration
│   ├── controllers/     # Route handlers
│   ├── middleware/      # Authentication, validation, etc.
│   ├── routes/          # API route definitions
│   └── server.js        # Main application file
├── .env.example         # Environment variables template
├── package.json         # Dependencies and scripts
└── README.md           # This file
```

### Adding New Endpoints

1. **Create Controller**
```javascript
// controllers/newController.js
const newFunction = async (req, res) => {
  try {
    // Implementation
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};
```

2. **Add Route**
```javascript
// routes/new.js
router.get('/', authenticateToken, newController.newFunction);
```

3. **Register Route**
```javascript
// routes/index.js
router.use('/new', require('./new'));
```

### Testing
```bash
# Run tests (when implemented)
npm test

# Test API endpoints
curl -X GET http://localhost:5000/api/health
```

## Deployment

### Environment Variables
```bash
# Production settings
NODE_ENV=production
PORT=5000
DB_SERVER=your-sql-server
DB_DATABASE=FieldReporter
DB_USER=your-db-user
DB_PASSWORD=your-secure-password
JWT_SECRET=your-super-secure-jwt-secret
CORS_ORIGIN=https://your-frontend-domain.com
```

### PM2 Deployment
```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start src/server.js --name fieldreporter-api

# Monitor
pm2 monit

# Logs
pm2 logs fieldreporter-api
```

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src/ ./src/
EXPOSE 5000
CMD ["node", "src/server.js"]
```

## Monitoring & Logging

### Health Check
```bash
curl http://localhost:5000/api/health
```

### Database Health
```javascript
// Returns database status and connection info
GET /api/health
```

### Logs
- Development: Console output with colors
- Production: Combined format for log aggregation
- Error logs: Separate error tracking

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check SQL Server is running
   - Verify connection string
   - Check firewall settings
   - Ensure database exists

2. **JWT Token Errors**
   - Check JWT_SECRET is set
   - Verify token hasn't expired
   - Check token format (Bearer token)

3. **Permission Denied**
   - Verify user role and permissions
   - Check department access rules
   - Ensure user is active

4. **Rate Limiting**
   - Check request frequency
   - Verify IP address
   - Adjust rate limit settings

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Database query logging
DB_DEBUG=true npm run dev
```

## Contributing

1. Follow existing code structure
2. Add input validation for all endpoints
3. Include error handling
4. Update documentation
5. Test with different user roles
6. Verify department access control

## Support

For issues and questions:
1. Check this documentation
2. Review error logs
3. Test with Postman/curl
4. Check database connectivity
5. Verify environment variables