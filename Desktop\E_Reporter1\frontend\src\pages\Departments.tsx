import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  Building2, 
  Users, 
  Plus, 
  Search, 
  Edit, 
  Trash2,
  ChevronRight,
  FolderOpen
} from 'lucide-react';

export function Departments() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null);

  // Mock data - replace with actual API calls
  const departments = [
    {
      id: '1',
      name: 'Tổng Công Ty',
      type: 'general',
      parent_id: null,
      users_count: 156,
      forms_count: 24,
      children: [
        {
          id: '2',
          name: 'Phòng IT',
          type: 'next',
          parent_id: '1',
          users_count: 45,
          forms_count: 12,
          children: [
            {
              id: '3',
              name: 'Team Development',
              type: 'direct',
              parent_id: '2',
              users_count: 25,
              forms_count: 8
            },
            {
              id: '4',
              name: 'Team Infrastructure',
              type: 'direct',
              parent_id: '2',
              users_count: 20,
              forms_count: 4
            }
          ]
        },
        {
          id: '5',
          name: '<PERSON><PERSON><PERSON>',
          type: 'next',
          parent_id: '1',
          users_count: 78,
          forms_count: 18,
          children: [
            {
              id: '6',
              name: 'Team QC',
              type: 'direct',
              parent_id: '5',
              users_count: 35,
              forms_count: 10
            },
            {
              id: '7',
              name: 'Team Production',
              type: 'direct',
              parent_id: '5',
              users_count: 43,
              forms_count: 8
            }
          ]
        }
      ]
    }
  ];

  const renderDepartment = (dept: any, level: number = 0) => {
    const hasChildren = dept.children && dept.children.length > 0;
    const isExpanded = selectedDepartment === dept.id;

    return (
      <div key={dept.id} className="mb-2">
        <div 
          className={`flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors cursor-pointer ${
            level > 0 ? 'ml-6' : ''
          }`}
          onClick={() => setSelectedDepartment(isExpanded ? null : dept.id)}
        >
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              dept.type === 'general' ? 'bg-blue-100' :
              dept.type === 'next' ? 'bg-green-100' : 'bg-purple-100'
            }`}>
              <Building2 className={`w-5 h-5 ${
                dept.type === 'general' ? 'text-blue-600' :
                dept.type === 'next' ? 'text-green-600' : 'text-purple-600'
              }`} />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{dept.name}</h3>
              <p className="text-sm text-gray-600 capitalize">{dept.type} Department</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>{dept.users_count} users</span>
              </div>
              <div className="flex items-center space-x-1">
                <FolderOpen className="w-4 h-4" />
                <span>{dept.forms_count} forms</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                <Edit className="w-4 h-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                <Trash2 className="w-4 h-4" />
              </button>
              {hasChildren && (
                <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform ${
                  isExpanded ? 'rotate-90' : ''
                }`} />
              )}
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-2">
            {dept.children.map((child: any) => renderDepartment(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Departments</h1>
          <p className="text-gray-600 mt-1">Manage organizational structure and hierarchy</p>
        </div>
        {(user?.role === 'admin' || user?.role === 'final_approval') && (
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Plus className="w-4 h-4" />
            <span>Add Department</span>
          </button>
        )}
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search departments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Department Tree */}
      <div className="space-y-4">
        {departments.map(dept => renderDepartment(dept))}
      </div>

      {/* Department Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Departments</p>
              <p className="text-2xl font-bold text-gray-900">7</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">156</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Forms</p>
              <p className="text-2xl font-bold text-gray-900">42</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <FolderOpen className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}